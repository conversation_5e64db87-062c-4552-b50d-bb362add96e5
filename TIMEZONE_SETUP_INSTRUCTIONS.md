# Timezone-Aware Coaching Messages - Setup Instructions

## Overview
The coaching message system has been updated to send messages at 3 PM in each user's local timezone. This document provides step-by-step instructions to complete the setup.

## Changes Made

### 1. Backend (Cloud Functions)
✅ **Completed**
- Updated `send_daily_coaching_messages` function to run every hour
- Added timezone checking logic to determine if it's 3 PM for each user
- Added duplicate prevention using `lastCoachingMessageDate` field
- Added `pytz` dependency for timezone handling

### 2. Frontend (Mobile App)
✅ **Completed**
- Updated `app/onboarding.tsx` to save timezone when diet preferences are saved
- Uses `expo-localization` (already installed) to detect timezone
- Saves timezone in parallel with diet preferences for efficiency

## Setup Steps

### Step 1: Deploy Cloud Functions

Deploy the updated Cloud Function:

```bash
cd functions
pip install -r requirements.txt  # Install pytz
cd ..
firebase deploy --only functions:send_daily_coaching_messages
```

### Step 2: Update Existing Users (One-Time Migration)

For existing users who don't have a timezone set:

#### Option A: Automatic Update on Next Onboarding (Recommended)
New users will automatically get their timezone saved when they complete onboarding. Existing users who haven't completed onboarding will get it when they do.

#### Option B: Manual Migration Script
For existing users who have already completed onboarding, you can run a one-time migration using Firebase Admin SDK or Firestore console to set a default timezone (e.g., "America/New_York" or "UTC").

## Testing

### Test the Timezone Detection

1. **Check Device Timezone**:
   ```typescript
   import * as Localization from 'expo-localization';
   console.log('Device timezone:', Localization.timezone);
   ```

2. **Verify Firestore Update**:
   - Complete onboarding in the app
   - Check the user document in Firestore
   - Verify the `timezone` field is set correctly

3. **Test Cloud Function**:
   - Manually set your user's timezone in Firestore to a timezone where it's currently 3 PM
   - Wait for the next hour to trigger
   - Check Cloud Function logs in Firebase Console
   - Verify you receive a coaching message

### Test Different Timezones

To test with different timezones, manually update the timezone in Firestore:

```typescript
import { doc, setDoc } from '@react-native-firebase/firestore';
import { db } from '@/firebase/firebaseConfig';

// Temporarily change your timezone for testing
await setDoc(doc(db, 'users', uid), { timezone: 'America/Los_Angeles' }, { merge: true });
```

## Monitoring

### Cloud Function Logs

Monitor the function execution in Firebase Console:
1. Go to Firebase Console → Functions
2. Click on `send_daily_coaching_messages`
3. View logs to see:
   - When the function runs (every hour)
   - Which users are checked
   - Which users receive messages
   - Any errors or skipped users

### Key Log Messages

- `Starting hourly coaching message check at {UTC_TIME}` - Function started
- `Generating coaching message for user {uid} (local time: {LOCAL_TIME})` - Message being sent
- `Already sent coaching message to user {uid} today` - Duplicate prevented
- `No timezone set for user {uid}, skipping` - User needs timezone
- `Hourly coaching message check completed. Messages sent: X` - Summary

## Troubleshooting

### Issue: Users Not Receiving Messages

**Check:**
1. User has `timezone` field in Firestore
2. Timezone is valid (check against IANA timezone list)
3. It's actually 3 PM in their timezone
4. `lastCoachingMessageDate` is not today's date
5. User has diet preferences set

**Solution:**
```typescript
// Check user document
import { doc, getDoc, setDoc } from '@react-native-firebase/firestore';
import { db } from '@/firebase/firebaseConfig';

const userDoc = await getDoc(doc(db, 'users', uid));
console.log('User data:', userDoc.data());

// Manually set timezone if needed
await setDoc(doc(db, 'users', uid), { timezone: 'America/New_York' }, { merge: true });
```

### Issue: Timezone Not Detected

**Error:** `Localization.timezone` returns undefined

**Solution:**
`expo-localization` should already be installed with Expo. If not:
```bash
npx expo install expo-localization
```

### Issue: Messages Sent Multiple Times

**Check:**
- `lastCoachingMessageDate` field is being updated
- Date format is consistent (YYYY-MM-DD)
- No race conditions (multiple function instances)

**Solution:**
Check Cloud Function logs for duplicate executions and verify the update logic.

## Optional Enhancements

### 1. User Timezone Settings Screen

Allow users to manually set their timezone:

```typescript
import { doc, setDoc } from '@react-native-firebase/firestore';
import { db } from '@/firebase/firebaseConfig';

const COMMON_TIMEZONES = [
  { label: 'Eastern Time (US)', value: 'America/New_York' },
  { label: 'Central Time (US)', value: 'America/Chicago' },
  { label: 'Mountain Time (US)', value: 'America/Denver' },
  { label: 'Pacific Time (US)', value: 'America/Los_Angeles' },
  // ... add more as needed
];

// In your settings screen
<Picker
  selectedValue={selectedTimezone}
  onValueChange={async (value) => {
    setSelectedTimezone(value);
    await setDoc(doc(db, 'users', uid), {
      timezone: value,
      timezoneUpdatedAt: new Date()
    }, { merge: true });
  }}
>
  {COMMON_TIMEZONES.map((tz) => (
    <Picker.Item key={tz.value} label={tz.label} value={tz.value} />
  ))}
</Picker>
```

### 2. Customizable Message Time

Allow users to choose when they want to receive coaching messages:

```typescript
// Add to user document
{
  timezone: 'America/New_York',
  coachingMessageHour: 15, // 3 PM (default)
}

// Update Cloud Function to check user's preferred hour
const preferredHour = user_data.get("coachingMessageHour", 15)
if user_hour != preferredHour:
    continue
```

### 3. Message Frequency Control

Let users choose how often they receive messages:

```typescript
// Add to user document
{
  timezone: 'America/New_York',
  coachingMessageFrequency: 'daily', // 'daily', 'weekly', 'never'
}
```

## Resources

- [IANA Timezone Database](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones)
- [expo-localization Documentation](https://docs.expo.dev/versions/latest/sdk/localization/)
- [Python pytz Documentation](https://pythonhosted.org/pytz/)
- [Firebase Cloud Functions Scheduling](https://firebase.google.com/docs/functions/schedule-functions)

## Support

For issues or questions:
1. Check Cloud Function logs in Firebase Console
2. Review this documentation
3. Check the detailed documentation in `functions/TIMEZONE_COACHING_MESSAGES.md`

