import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { FirebaseAuthTypes, signInAnonymously, onAuthStateChanged } from '@react-native-firebase/auth';
import { setDoc, doc } from '@react-native-firebase/firestore';
import { auth, db } from '@/firebase/firebaseConfig';
import analyticsService from '@/services/AnalyticsService';
import logger from '@/services/logger';
import { USER_PROPERTIES } from '@/constants/Analytics';

interface AuthContextType {
  user: FirebaseAuthTypes.User | null;
  loading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<FirebaseAuthTypes.User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Sign in anonymously if no user is present
    const initializeAuth = async () => {
      try {
        if (!auth.currentUser) {
          await signInAnonymously(auth);
        }
      } catch (error) {
        logger.authError(error instanceof Error ? error.message : 'Unknown auth error', {
          action: 'anonymous_signin',
        });
        setLoading(false);
      }
    };

    const unsubscribe = onAuthStateChanged(auth, async (currentUser: FirebaseAuthTypes.User | null) => {
      setUser(currentUser);
      const uid = currentUser?.uid;
      setLoading(false);

      if (uid) {
        try {
          // Update user document in Firestore
          const userDocRef = doc(db, 'users', uid);
          await setDoc(
            userDocRef,
            {
              lastActiveAt: new Date(),
            },
            { merge: true }
          );

          // Set user ID for analytics and crashlytics
          await Promise.all([analyticsService.setUserId(uid), logger.setUserId(uid)]);

          // Track user sign in
          await analyticsService.trackUserSignIn('anonymous');

          // Set initial user properties
          await analyticsService.setUserProperties({
            [USER_PROPERTIES.USER_TYPE]: 'anonymous',
            [USER_PROPERTIES.LAST_ACTIVE_DATE]: new Date().toISOString().split('T')[0],
          });

          logger.info(`User authenticated and tracked: ${uid}`);
        } catch (error) {
          logger.error('Error setting up user tracking', error as Error, {
            userId: uid,
            action: 'user_tracking_setup',
          });
        }
      }
    });

    initializeAuth();

    return () => unsubscribe();
  }, []);

  const value = {
    user,
    loading,
    isAuthenticated: !!user,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
