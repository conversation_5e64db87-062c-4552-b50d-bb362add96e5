import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { GroceryListService } from '@/services/GroceryListService';
import { GroceryItem, Tag } from '@/components/types';
import { useAuth } from '@/contexts/AuthContext';
import performanceService from '@/services/PerformanceService';
import analyticsService from '@/services/AnalyticsService';
import logger from '@/services/logger';

interface GroceryListContextType {
  groceryItemCount: number;
  isUserAuthenticated: boolean;
  updateGroceryItemCount: () => Promise<GroceryItem[]>;
  addItem: (itemName: string, tags?: Tag[]) => Promise<GroceryItem[]>;
  addItems: (itemNames: string[], tags?: Tag[]) => Promise<GroceryItem[]>;
  toggleItemChecked: (itemName: string) => Promise<GroceryItem[]>;
  removeCheckedItems: () => Promise<GroceryItem[]>;
  updateItemTags: (itemName: string, tags: Tag[]) => Promise<GroceryItem[]>;
  incrementItemQuantity: (itemName: string) => Promise<GroceryItem[]>;
  decrementItemQuantity: (itemName: string) => Promise<GroceryItem[]>;
  removeItem: (itemName: string) => Promise<GroceryItem[]>;
}

const GroceryListContext = createContext<GroceryListContextType | undefined>(undefined);

export const useGroceryList = () => {
  const context = useContext(GroceryListContext);
  if (!context) {
    throw new Error('useGroceryList must be used within a GroceryListProvider');
  }
  return context;
};

interface GroceryListProviderProps {
  children: ReactNode;
}

export const GroceryListProvider: React.FC<GroceryListProviderProps> = ({ children }) => {
  const [groceryItemCount, setGroceryItemCount] = useState(0);
  const { isAuthenticated } = useAuth();

  // Update grocery list when authentication state changes
  useEffect(() => {
    if (isAuthenticated) {
      updateGroceryItemCount();
    } else {
      setGroceryItemCount(0);
    }
  }, [isAuthenticated]);

  // Function to update the grocery item count and return the list
  const updateGroceryItemCount = async (): Promise<GroceryItem[]> => {
    const groceryTraceId = await performanceService.startTrace('grocery_list_load');
    const startTime = Date.now();

    try {
      if (!isAuthenticated) {
        logger.debug('Skipping grocery list fetch - user not authenticated yet');
        await performanceService.stopTrace(groceryTraceId, false, { reason: 'not_authenticated' });
        return [];
      }

      const groceryList = await GroceryListService.getGroceryList();
      setGroceryItemCount(groceryList.length);

      const loadTime = Date.now() - startTime;

      // Track successful grocery list load
      await Promise.all([
        performanceService.stopTrace(groceryTraceId, true, {
          item_count: groceryList.length.toString(),
        }),
        analyticsService.trackEvent('grocery_list_loaded', {
          item_count: groceryList.length,
          load_time_ms: loadTime,
        }),
      ]);

      return groceryList;
    } catch (error) {
      const loadTime = Date.now() - startTime;

      // Track grocery list load error
      logger.dataError('grocery_list_load', error instanceof Error ? error.message : 'Unknown error', {
        additionalData: { loadTime },
      });

      await performanceService.stopTrace(groceryTraceId, false, {
        error_message: error instanceof Error ? error.message : 'Unknown error',
      });

      return [];
    }
  };

  // Wrapper for GroceryListService.addItem that also updates the count
  const addItem = async (itemName: string, tags: Tag[] = []) => {
    try {
      const updatedList = await GroceryListService.addItem(itemName, tags);
      setGroceryItemCount(updatedList.length);
      return updatedList;
    } catch (error) {
      logger.dataError('grocery_item_add', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  };

  // Wrapper for GroceryListService.addItems that also updates the count
  const addItems = async (itemNames: string[], tags: Tag[] = []) => {
    try {
      const updatedList = await GroceryListService.addItems(itemNames, tags);
      setGroceryItemCount(updatedList.length);
      return updatedList;
    } catch (error) {
      logger.dataError('grocery_items_bulk_add', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  };

  // Wrapper for GroceryListService.toggleItemChecked
  const toggleItemChecked = async (itemName: string) => {
    try {
      const updatedList = await GroceryListService.toggleItemChecked(itemName);
      // Count doesn't change when toggling checked status
      return updatedList;
    } catch (error) {
      logger.dataError('grocery_item_toggle', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  };

  // Wrapper for GroceryListService.removeCheckedItems
  const removeCheckedItems = async () => {
    try {
      const updatedList = await GroceryListService.removeCheckedItems();
      setGroceryItemCount(updatedList.length);
      return updatedList;
    } catch (error) {
      logger.dataError('grocery_checked_items_remove', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  };

  // Wrapper for GroceryListService.updateItemTags
  const updateItemTags = async (itemName: string, tags: Tag[]) => {
    try {
      const updatedList = await GroceryListService.updateItemTags(itemName, tags);
      // Count doesn't change when updating tags
      return updatedList;
    } catch (error) {
      logger.dataError('grocery_item_tags_update', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  };

  // Wrapper for GroceryListService.incrementItemQuantity
  const incrementItemQuantity = async (itemName: string) => {
    try {
      const updatedList = await GroceryListService.incrementItemQuantity(itemName);
      // Count doesn't change when updating quantity
      return updatedList;
    } catch (error) {
      logger.dataError('grocery_item_quantity_increment', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  };

  // Wrapper for GroceryListService.decrementItemQuantity
  const decrementItemQuantity = async (itemName: string) => {
    try {
      const updatedList = await GroceryListService.decrementItemQuantity(itemName);
      // Count doesn't change when updating quantity
      return updatedList;
    } catch (error) {
      logger.dataError('grocery_item_quantity_decrement', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  };

  // Wrapper for GroceryListService.removeItem
  const removeItem = async (itemName: string) => {
    try {
      const updatedList = await GroceryListService.removeItem(itemName);
      // Update count since an item was removed
      const newCount = updatedList.reduce((sum, item) => sum + item.quantity, 0);
      setGroceryItemCount(newCount);
      return updatedList;
    } catch (error) {
      logger.dataError('grocery_item_remove', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  };

  const value = {
    groceryItemCount,
    isUserAuthenticated: isAuthenticated,
    updateGroceryItemCount,
    addItem,
    addItems,
    toggleItemChecked,
    removeCheckedItems,
    updateItemTags,
    incrementItemQuantity,
    decrementItemQuantity,
    removeItem,
  };

  return <GroceryListContext.Provider value={value}>{children}</GroceryListContext.Provider>;
};
