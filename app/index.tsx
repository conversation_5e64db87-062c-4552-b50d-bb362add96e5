import { useEffect } from 'react';
import { useRouter } from 'expo-router';
import { COLLECTIONS, firestoreRepository } from '@/repositories/firestoreRepository';
import { useAuth } from '@/contexts/AuthContext';
import analyticsService from '@/services/AnalyticsService';
import logger from '@/services/logger';
import { SCREEN_NAMES } from '@/constants/Analytics';

export default function IndexRedirect() {
  const router = useRouter();
  const { user, loading } = useAuth();

  useEffect(() => {
    const checkAndRedirect = async () => {
      // Wait for auth to finish loading
      if (loading) return;

      try {
        // Track screen view for index redirect
        await analyticsService.trackScreenView(SCREEN_NAMES.INDEX);

        if (user) {
          const doc = await firestoreRepository.getDocument(COLLECTIONS.DIET_PREFERENCES, user.uid);
          if (doc) {
            router.replace('/(tabs)');
          } else {
            router.replace('/onboarding');
          }
        }
      } catch (err) {
        logger.uiError(SCREEN_NAMES.INDEX, err instanceof Error ? err.message : 'Unknown redirect error', {
          userId: user?.uid,
          action: 'user_redirect',
        });
      }
    };

    checkAndRedirect();
  }, [user, loading, router]);

  return null;
}
