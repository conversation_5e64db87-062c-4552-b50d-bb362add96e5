import { useEffect } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { useCallback } from 'react';
import { useUnifiedChat } from '@/hooks/useUnifiedChat';
import ChatUI from '@/components/ChatUI';
import analyticsService from '@/services/AnalyticsService';
import logger from '@/services/logger';
import { SCREEN_NAMES } from '@/constants/Analytics';
import { ConversationService } from '@/services/ConversationService';
import { useAuth } from '@/contexts/AuthContext';

export default function ChatScreen() {
  const { user } = useAuth();
  const { messages, messageInput, setMessageInput, handleSend, isLoading, isSending, conversationId } =
    useUnifiedChat();

  // Track screen view on component mount
  useEffect(() => {
    const trackScreenView = async () => {
      try {
        await analyticsService.trackScreenView(SCREEN_NAMES.CHAT);
      } catch (error) {
        logger.error('Error tracking chat screen view', error instanceof Error ? error : undefined);
      }
    };

    trackScreenView();
  }, []);

  // Mark messages as read when screen is focused
  useFocusEffect(
    useCallback(() => {
      const markAsRead = async () => {
        if (user?.uid && conversationId) {
          try {
            await ConversationService.markAsRead(conversationId, user.uid);
            logger.info('Messages marked as read', {
              additionalData: { conversationId, userId: user.uid },
            });
          } catch (error) {
            logger.error('Error marking messages as read', error as Error, {
              additionalData: { conversationId, userId: user.uid },
            });
          }
        }
      };

      markAsRead();
    }, [user?.uid, conversationId])
  );

  return (
    <ChatUI
      messages={messages}
      inputValue={messageInput}
      onInputChange={setMessageInput}
      onSendMessage={handleSend}
      isLoading={isLoading}
      isSending={isSending}
      placeholder='Chat with your Chefpal...'
    />
  );
}
