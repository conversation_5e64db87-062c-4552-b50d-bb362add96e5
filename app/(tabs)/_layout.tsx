import { Tabs } from 'expo-router';
import React from 'react';
import { Platform, View } from 'react-native';

import TabBarBackground from '@/components/ui/TabBarBackground';
import { TabBarBadge } from '@/components/TabBarBadge';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useUnreadCount } from '@/hooks/useUnreadCount';
import HomeIcon from '@/assets/images/icons/home.svg';
import ChefHat from '@/assets/images/icons/chef-hat.svg';
import InventoryIcon from '@/assets/images/icons/inventory.svg';

export default function TabLayout() {
  const colorScheme = useColorScheme();
  const unreadCount = useUnreadCount();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
        tabBarInactiveTintColor: Colors[colorScheme ?? 'light'].tabIconDefault,
        headerShown: false,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            // Use a transparent background on iOS to show the blur effect
            position: 'absolute',
          },
          default: {},
        }),
      }}
    >
      <Tabs.Screen
        name='index'
        options={{
          title: 'Home',
          tabBarIcon: ({ color }) => <HomeIcon fill={color} />,
        }}
      />
      <Tabs.Screen
        name='inventory'
        options={{
          title: 'Inventory',
          tabBarIcon: ({ color }) => <InventoryIcon color={color} />,
        }}
      />
      <Tabs.Screen
        name='chat'
        options={{
          title: 'Chat',
          tabBarIcon: ({ color }) => (
            <View>
              <ChefHat fill={color} />
              <TabBarBadge count={unreadCount} />
            </View>
          ),
        }}
      />
    </Tabs>
  );
}
