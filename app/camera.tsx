import React, { useState, useEffect } from 'react';
import { useRouter } from 'expo-router';
import LoadingAnimation from '@/components/LoadingAnimation';
import CaptureView from '@/components/CaptureView';
import { analyzeImagesAsync } from '@/services/analyzeImages';
import { InventoryService } from '@/services/InventoryService';
import { useInventory } from '@/contexts/InventoryContext';
import { InventoryItem } from '@/components/types';
import analyticsService from '@/services/AnalyticsService';
import performanceService from '@/services/PerformanceService';
import logger from '@/services/logger';
import { SCREEN_NAMES, ANALYTICS_EVENTS, ANALYTICS_PARAMS } from '@/constants/Analytics';

export default function CameraScreen() {
  const router = useRouter();
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const { updateInventoryWithCategories } = useInventory();

  // Track screen view on component mount
  useEffect(() => {
    const trackScreenView = async () => {
      try {
        await analyticsService.trackScreenView(SCREEN_NAMES.CAMERA);
      } catch (error) {
        logger.error('Error tracking camera screen view', error instanceof Error ? error : undefined);
      }
    };

    trackScreenView();
  }, []);

  const handlePhotosTaken = async (photos: string[]) => {
    setIsAnalyzing(true);
    const analysisTraceId = await performanceService.trackImageAnalysis(photos.length);
    const startTime = Date.now();

    try {
      const ingredients = await analyzeImagesAsync(photos);

      // Convert ingredients to InventoryItem format
      const newItems: InventoryItem[] = ingredients.map((ingredient) => ({
        name: ingredient.name,
        quantity: 1, // Default quantity
      }));

      // Prepare the updated inventory locally first
      const currentInventory = await InventoryService.getUserInventory();
      const updatedInventory = [...currentInventory];
      const currentTime = Date.now();

      // Process new items locally to get the final inventory state
      for (const newItem of newItems) {
        const existingItemIndex = updatedInventory.findIndex(
          (i) => i.name.toLowerCase() === newItem.name.toLowerCase()
        );

        if (existingItemIndex >= 0) {
          // Update existing item - only update quantity if current is 0
          if (updatedInventory[existingItemIndex].quantity === 0) {
            updatedInventory[existingItemIndex] = {
              ...updatedInventory[existingItemIndex],
              quantity: newItem.quantity,
            };
          }
        } else {
          // Add new item
          updatedInventory.push({
            ...newItem,
            addedAt: currentTime,
          });
        }
      }

      // Run backend update and LLM categorization in parallel
      const [, categorizedInventory] = await Promise.all([
        InventoryService.addOrUpdateMultipleItems(newItems),
        InventoryService.categorizeInventory(updatedInventory),
      ]);

      // Update local state with properly categorized inventory
      // Use the new function that accepts both items and categories to avoid re-categorization
      updateInventoryWithCategories(updatedInventory, categorizedInventory);

      // Track successful photo analysis
      const analysisTime = Date.now() - startTime;
      await Promise.all([
        analyticsService.trackPhotosAnalyzed(photos.length, ingredients.length, analysisTime),
        performanceService.stopTrace(analysisTraceId, true, {
          ingredients_found: ingredients.length.toString(),
          photos_processed: photos.length.toString(),
        }),
      ]);

      // Navigate back to inventory tab
      router.push('/(tabs)/inventory');
    } catch (error) {
      const analysisTime = Date.now() - startTime;

      // Track error and stop performance trace
      logger.dataError('photo_analysis', error instanceof Error ? error.message : 'Unknown analysis error', {
        screen: SCREEN_NAMES.CAMERA,
        additionalData: { photoCount: photos.length },
      });

      await Promise.all([
        performanceService.stopTrace(analysisTraceId, false, {
          error_message: error instanceof Error ? error.message : 'Unknown error',
        }),
        analyticsService.trackEvent(ANALYTICS_EVENTS.IMAGE_ANALYSIS_ERROR, {
          [ANALYTICS_PARAMS.PHOTOS_COUNT]: photos.length,
          [ANALYTICS_PARAMS.ANALYSIS_TIME_MS]: analysisTime,
          [ANALYTICS_PARAMS.ERROR_MESSAGE]: error instanceof Error ? error.message : 'Unknown error',
        }),
      ]);

      router.back();
    } finally {
      setIsAnalyzing(false);
    }
  };

  if (isAnalyzing) {
    return (
      <LoadingAnimation
        source={require('../assets/images/gifs/bounce-veggie.gif')}
        message='Analyzing and organizing your food items...'
      />
    );
  }

  return <CaptureView onPhotosTaken={handlePhotosTaken} onClose={() => router.push('/(tabs)/inventory')} />;
}
