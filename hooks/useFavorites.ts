import { useState, useEffect } from 'react';
import { SavedRecipesService } from '@/services/SavedRecipesService';
import { Recipe, InstructionType } from '@/components/types';
import { useAuth } from '@/contexts/AuthContext';
import { generateRecipeDetailsAsync } from '@/services/generateRecipes';
import analyticsService from '@/services/AnalyticsService';
import logger from '@/services/logger';

export interface UseFavoritesReturn {
  favorites: Set<string>;
  toggleFavorite: (recipeId: string, e: any, recipe?: Recipe, lastResponseId?: string | null) => void;
  loadingFavorites: boolean;
  generatingDetails: Set<string>;
}

export interface UseFavoritesProps {
  onRecipeSaved?: () => void;
  onRecipeDetailsGenerated?: (recipeId: string, ingredients: any[], instructions: any) => void;
}

export const useFavorites = (props?: UseFavoritesProps): UseFavoritesReturn => {
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [loadingFavorites, setLoadingFavorites] = useState(true);
  const [generatingDetails, setGeneratingDetails] = useState<Set<string>>(new Set());
  const { user } = useAuth();
  const { onRecipeSaved, onRecipeDetailsGenerated } = props || {};

  // Helper function to check if a recipe has complete instructions
  const hasCompleteInstructions = (recipe: Recipe): boolean => {
    return (
      recipe.ingredients.length > 0 &&
      recipe.instructions[InstructionType.HIGH_LEVEL] !== '' &&
      recipe.instructions[InstructionType.HIGH_LEVEL] !== 'Instructions not available'
    );
  };

  // Load saved recipe IDs when user is available
  useEffect(() => {
    const loadSavedRecipes = async () => {
      if (!user) {
        setFavorites(new Set());
        setLoadingFavorites(false);
        return;
      }

      try {
        setLoadingFavorites(true);
        const savedRecipeIds = await SavedRecipesService.getSavedRecipeIds();
        setFavorites(new Set(savedRecipeIds));
      } catch (error) {
        logger.dataError('saved_recipes_load', error instanceof Error ? error.message : 'Unknown error');
        setFavorites(new Set());
      } finally {
        setLoadingFavorites(false);
      }
    };

    loadSavedRecipes();
  }, [user]);

  const toggleFavorite = async (recipeId: string, e: any, recipe?: Recipe, lastResponseId?: string | null) => {
    e.stopPropagation();

    if (!user) {
      logger.warn('Cannot toggle favorite: User not authenticated');
      return;
    }

    try {
      const newFavorites = new Set(favorites);

      if (favorites.has(recipeId)) {
        // Remove from favorites
        await SavedRecipesService.unsaveRecipe(recipeId);
        newFavorites.delete(recipeId);
        setFavorites(newFavorites);

        // Track recipe unfavorited
        await analyticsService.trackRecipeFavorited(recipeId, false);
      } else {
        // Add to favorites
        await handleAddToFavorites(recipe, recipeId, newFavorites, lastResponseId);
      }
    } catch (error) {
      logger.dataError('recipe_favorite_toggle', error instanceof Error ? error.message : 'Unknown error', {
        additionalData: { recipeId, action: favorites.has(recipeId) ? 'unfavorite' : 'favorite' },
      });
    }
  };

  const handleAddToFavorites = async (
    recipe: Recipe | undefined,
    recipeId: string,
    newFavorites: Set<string>,
    lastResponseId: string | null | undefined
  ) => {
    if (!recipe) {
      console.error('Recipe object is required to save a recipe');
      return;
    }

    // Update favorites state immediately for UI feedback
    newFavorites.add(recipeId);
    setFavorites(newFavorites);

    // Save the recipe (potentially incomplete) first for immediate favoriting
    await SavedRecipesService.saveRecipe(recipe);

    // Track recipe favorited
    await analyticsService.trackRecipeFavorited(recipeId, true);

    // Call the callback to refresh saved recipes if provided
    onRecipeSaved?.();

    // Generate details in background if recipe is incomplete
    if (!hasCompleteInstructions(recipe) && lastResponseId) {
      await generateDetailsInBackground(recipe, lastResponseId);
    } else if (!lastResponseId) {
      logger.info('No lastResponseId available, recipe saved without generating details');
    }
  };

  const generateDetailsInBackground = async (recipe: Recipe, lastResponseId: string) => {
    console.log('Recipe missing instructions, generating details in background...');

    // Mark recipe as generating details
    setGeneratingDetails((prev) => new Set(prev).add(recipe.id));

    try {
      // Generate recipe details
      const details = await generateRecipeDetailsAsync(recipe.id, recipe.title, recipe.mealType, lastResponseId);

      // Create complete recipe with generated details
      const completeRecipe = {
        ...recipe,
        ingredients: details.ingredients,
        instructions: details.instructions,
      };

      logger.info('Recipe details generated successfully, updating saved recipe...');

      // Update the saved recipe with complete details
      await SavedRecipesService.saveRecipe(completeRecipe);

      // Update local recipe state
      onRecipeDetailsGenerated?.(recipe.id, details.ingredients, details.instructions);

      // Refresh saved recipes to show updated details
      onRecipeSaved?.();
    } catch (error) {
      logger.dataError('recipe_details_generation', error instanceof Error ? error.message : 'Unknown error', {
        additionalData: { recipeId: recipe.id },
      });
    } finally {
      // Always remove from generating details set
      setGeneratingDetails((prev) => {
        const newSet = new Set(prev);
        newSet.delete(recipe.id);
        return newSet;
      });
    }
  };

  return {
    favorites,
    toggleFavorite,
    loadingFavorites,
    generatingDetails,
  };
};
