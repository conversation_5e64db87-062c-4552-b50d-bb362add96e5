import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import firestore from '@react-native-firebase/firestore';
import { COLLECTIONS, SUBCOLLECTIONS } from '@/constants/FirestoreCollections';
import logger from '@/services/logger';

/**
 * Hook to track total unread message count across all conversations
 * Subscribes to the user's inbox and calculates the sum of unread counts
 * 
 * @returns {number} Total unread message count
 */
export function useUnreadCount(): number {
  const { user } = useAuth();
  const [unreadCount, setUnreadCount] = useState<number>(0);

  useEffect(() => {
    if (!user?.uid) {
      setUnreadCount(0);
      return;
    }

    // Subscribe to user's inbox to track unread counts
    const unsubscribe = firestore()
      .collection(COLLECTIONS.USERS)
      .doc(user.uid)
      .collection(SUBCOLLECTIONS.USERS.INBOX)
      .onSnapshot(
        (querySnapshot) => {
          let totalUnread = 0;

          querySnapshot.forEach((doc) => {
            const data = doc.data();
            const count = data?.unreadCount || 0;
            totalUnread += count;
          });

          setUnreadCount(totalUnread);
          
          logger.info('Unread count updated', {
            additionalData: { 
              userId: user.uid, 
              totalUnread,
              conversationCount: querySnapshot.size 
            },
          });
        },
        (error) => {
          logger.error('Error subscribing to inbox for unread count', error as Error, {
            userId: user.uid,
          });
          // Don't reset count on error, keep the last known value
        }
      );

    return () => {
      unsubscribe();
    };
  }, [user?.uid]);

  return unreadCount;
}

