import { useEffect, useRef } from 'react';
import { useRouter } from 'expo-router';
import { AppState, AppStateStatus } from 'react-native';
import { PushNotificationService } from '@/services/PushNotificationService';
import { useAuth } from '@/contexts/AuthContext';
import logger from '@/services/logger';

/**
 * Hook to handle push notifications
 * Manages token registration, notification handlers, and deep linking
 */
export function usePushNotifications() {
  const { user } = useAuth();
  const router = useRouter();
  const appState = useRef(AppState.currentState);

  useEffect(() => {
    if (!user?.uid) return;

    // Initialize push notifications
    const initializeNotifications = async () => {
      try {
        await PushNotificationService.initialize();
        logger.info('Push notifications initialized');
      } catch (error) {
        logger.error('Error initializing push notifications', error as Error);
      }
    };

    initializeNotifications();

    // Handle notification tap when app is in background
    const unsubscribeOpened = PushNotificationService.onNotificationOpenedApp(
      (remoteMessage) => {
        handleNotificationTap(remoteMessage);
      }
    );

    // Handle foreground notifications
    const unsubscribeForeground = PushNotificationService.onForegroundMessage(
      (remoteMessage) => {
        logger.info('Foreground notification received', {
          title: remoteMessage.notification?.title,
          body: remoteMessage.notification?.body,
        });
        // You could show an in-app notification here
        // For now, we'll just log it
      }
    );

    // Check if app was opened from a notification
    PushNotificationService.getInitialNotification().then((remoteMessage) => {
      if (remoteMessage) {
        handleNotificationTap(remoteMessage);
      }
    });

    // Handle app state changes to update badge count
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      unsubscribeOpened();
      unsubscribeForeground();
      subscription.remove();
    };
  }, [user?.uid]);

  const handleAppStateChange = async (nextAppState: AppStateStatus) => {
    if (
      appState.current.match(/inactive|background/) &&
      nextAppState === 'active'
    ) {
      // App has come to the foreground
      // Clear badge when app is opened
      await PushNotificationService.setBadgeCount(0);
    }
    appState.current = nextAppState;
  };

  const handleNotificationTap = (remoteMessage: any) => {
    try {
      const data = remoteMessage.data;
      
      if (!data) {
        logger.info('No data in notification');
        return;
      }

      logger.info('Handling notification tap', { data });

      // Handle coaching message notification
      if (data.type === 'coaching_message' && data.conversationId) {
        // Navigate to chat screen
        router.push('/(tabs)/chat');
        logger.info('Navigated to chat from notification', {
          conversationId: data.conversationId,
        });
      }
    } catch (error) {
      logger.error('Error handling notification tap', error as Error);
    }
  };
}

