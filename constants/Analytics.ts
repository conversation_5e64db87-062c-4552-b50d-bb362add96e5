/**
 * Firebase Analytics Constants
 * Centralized definitions for event names, parameters, and user properties
 */

// Analytics Event Names
export const ANALYTICS_EVENTS = {
  // Authentication & Onboarding
  USER_SIGNED_IN: 'user_signed_in',
  ONBOARDING_STARTED: 'onboarding_started',
  ONBOARDING_COMPLETED: 'onboarding_completed',
  DIET_PREFERENCES_SAVED: 'diet_preferences_saved',
  
  // Navigation & Screen Views
  SCREEN_VIEW: 'screen_view',
  TAB_SWITCHED: 'tab_switched',
  
  // Recipe Interactions
  RECIPE_GENERATED: 'recipe_generated',
  RECIPE_DETAILS_VIEWED: 'recipe_details_viewed',
  RECIPE_FAVORITED: 'recipe_favorited',
  RECIPE_UNFAVORITED: 'recipe_unfavorited',
  RECIPE_SHARED: 'recipe_shared',
  MEAL_TYPE_FILTERED: 'meal_type_filtered',
  RECIPE_SOURCE_SWITCHED: 'recipe_source_switched',
  RECIPE_REFRESH: 'recipe_refresh',
  RECIPE_GENERATION_STARTED: 'recipe_generation_started',
  RECIPE_GENERATION_COMPLETED: 'recipe_generation_completed',
  
  // Inventory Management
  CAMERA_OPENED: 'camera_opened',
  PHOTOS_ANALYZED: 'photos_analyzed',
  INVENTORY_ITEM_ADDED: 'inventory_item_added',
  INVENTORY_REFRESHED: 'inventory_refreshed',
  INVENTORY_CATEGORIZED: 'inventory_categorized',
  INGREDIENT_AVAILABILITY_CHECKED: 'ingredient_availability_checked',
  INVENTORY_LOADED: 'inventory_loaded',
  
  // Grocery List
  GROCERY_ITEM_ADDED: 'grocery_item_added',
  GROCERY_ITEM_CHECKED: 'grocery_item_checked',
  GROCERY_ITEM_REMOVED: 'grocery_item_removed',
  GROCERY_LIST_CLEARED: 'grocery_list_cleared',
  GROCERY_ITEM_QUANTITY_CHANGED: 'grocery_item_quantity_changed',
  GROCERY_LIST_LOADED: 'grocery_list_loaded',
  
  // Chat & AI Interactions
  CHAT_MESSAGE_SENT: 'chat_message_sent',
  AI_RESPONSE_RECEIVED: 'ai_response_received',
  USER_FEEDBACK_SUBMITTED: 'user_feedback_submitted',
  CHAT_CONVERSATION_STARTED: 'chat_conversation_started',
  
  // Errors & Performance
  API_ERROR: 'api_error',
  IMAGE_ANALYSIS_ERROR: 'image_analysis_error',
  RECIPE_GENERATION_ERROR: 'recipe_generation_error',
  NETWORK_ERROR: 'network_error',
  AUTHENTICATION_ERROR: 'authentication_error',
  
  // Performance Traces
  APP_STARTUP: 'app_startup',
  RECIPE_GENERATION_TRACE: 'recipe_generation_trace',
  IMAGE_ANALYSIS_TRACE: 'image_analysis_trace',
  DATA_LOADING_TRACE: 'data_loading_trace',
  PERFORMANCE_TRACE: 'performance_trace',
} as const;

// Analytics Parameter Names
export const ANALYTICS_PARAMS = {
  // Screen & Navigation
  SCREEN_NAME: 'screen_name',
  PREVIOUS_SCREEN: 'previous_screen',
  TAB_NAME: 'tab_name',
  NAVIGATION_SOURCE: 'navigation_source',
  
  // Recipe Parameters
  RECIPE_ID: 'recipe_id',
  RECIPE_TITLE: 'recipe_title',
  MEAL_TYPE: 'meal_type',
  RECIPE_SOURCE: 'recipe_source',
  GENERATION_TIME_MS: 'generation_time_ms',
  RECIPE_COUNT: 'recipe_count',
  CALORIES: 'calories',
  TIME_IN_MINUTES: 'time_in_minutes',
  COMPATIBLE_DIETS: 'compatible_diets',
  INSTRUCTION_TYPE: 'instruction_type',
  SERVINGS: 'servings',
  
  // Inventory Parameters
  ITEM_NAME: 'item_name',
  ITEM_COUNT: 'item_count',
  CATEGORY_NAME: 'category_name',
  PHOTOS_COUNT: 'photos_count',
  ANALYSIS_TIME_MS: 'analysis_time_ms',
  INGREDIENTS_FOUND: 'ingredients_found',
  AVAILABILITY_STATUS: 'availability_status',
  
  // Grocery List Parameters
  QUANTITY: 'quantity',
  ITEM_TAGS: 'item_tags',
  LIST_SIZE: 'list_size',
  CHECKED_ITEMS_COUNT: 'checked_items_count',
  
  // Chat Parameters
  MESSAGE_LENGTH: 'message_length',
  RESPONSE_TIME_MS: 'response_time_ms',
  FEEDBACK_TYPE: 'feedback_type',
  FEEDBACK_RATING: 'feedback_rating',
  CONVERSATION_LENGTH: 'conversation_length',
  
  // Error Parameters
  ERROR_CODE: 'error_code',
  ERROR_MESSAGE: 'error_message',
  ERROR_CONTEXT: 'error_context',
  API_ENDPOINT: 'api_endpoint',
  HTTP_STATUS: 'http_status',
  
  // Performance Parameters
  TRACE_NAME: 'trace_name',
  DURATION_MS: 'duration_ms',
  SUCCESS: 'success',
  MEMORY_USAGE: 'memory_usage',
  
  // User Behavior
  SESSION_DURATION: 'session_duration',
  FEATURE_USED: 'feature_used',
  USER_JOURNEY_STEP: 'user_journey_step',
  DIET_TYPE: 'diet_type',
  COOKING_EXPERIENCE: 'cooking_experience',
} as const;

// User Properties
export const USER_PROPERTIES = {
  USER_TYPE: 'user_type',
  ONBOARDING_COMPLETED: 'onboarding_completed',
  DIET_PREFERENCES_SET: 'diet_preferences_set',
  TOTAL_RECIPES_GENERATED: 'total_recipes_generated',
  TOTAL_INVENTORY_ITEMS: 'total_inventory_items',
  TOTAL_GROCERY_ITEMS: 'total_grocery_items',
  LAST_ACTIVE_DATE: 'last_active_date',
  APP_VERSION: 'app_version',
  PREFERRED_MEAL_TYPE: 'preferred_meal_type',
  DIET_TYPE: 'diet_type',
  COOKING_EXPERIENCE: 'cooking_experience',
  DAYS_SINCE_INSTALL: 'days_since_install',
  FEATURE_USAGE_COUNT: 'feature_usage_count',
} as const;

// Screen Names
export const SCREEN_NAMES = {
  HOME: 'Home',
  INVENTORY: 'Inventory',
  CHAT: 'Chat',
  CAMERA: 'Camera',
  GROCERY_LIST: 'Grocery_List',
  ONBOARDING: 'Onboarding',
  INDEX: 'Index_Redirect',
} as const;

// Custom Dimensions for Enhanced Analytics
export const CUSTOM_DIMENSIONS = {
  USER_SEGMENT: 'user_segment',
  FEATURE_FLAG: 'feature_flag',
  EXPERIMENT_VARIANT: 'experiment_variant',
  USER_COHORT: 'user_cohort',
} as const;

// Error Categories for Better Debugging
export const ERROR_CATEGORIES = {
  NETWORK: 'network',
  AUTHENTICATION: 'authentication',
  API: 'api',
  UI: 'ui',
  DATA_PROCESSING: 'data_processing',
  PERMISSION: 'permission',
  STORAGE: 'storage',
} as const;

// Performance Trace Names
export const PERFORMANCE_TRACES = {
  APP_STARTUP: 'app_startup',
  RECIPE_GENERATION: 'recipe_generation',
  IMAGE_ANALYSIS: 'image_analysis',
  DATA_SYNC: 'data_sync',
  SCREEN_LOAD: 'screen_load',
  API_CALL: 'api_call',
} as const;

// Event parameter value constraints (for validation)
export const PARAM_CONSTRAINTS = {
  MAX_STRING_LENGTH: 100,
  MAX_ARRAY_LENGTH: 25,
  MAX_CUSTOM_PARAMS: 25,
} as const;

// Type definitions for analytics events
export type AnalyticsEventName = typeof ANALYTICS_EVENTS[keyof typeof ANALYTICS_EVENTS];
export type AnalyticsParamName = typeof ANALYTICS_PARAMS[keyof typeof ANALYTICS_PARAMS];
export type UserPropertyName = typeof USER_PROPERTIES[keyof typeof USER_PROPERTIES];
export type ScreenName = typeof SCREEN_NAMES[keyof typeof SCREEN_NAMES];

// Analytics event parameter interface
export interface AnalyticsEventParams {
  [key: string]: string | number | boolean | string[] | undefined;
}

// User properties interface
export interface UserProperties {
  [key: string]: string | number | boolean | undefined;
}
