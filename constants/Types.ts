export enum Roles {
  user = 'user',
  assistant = 'assistant',
  developer = 'developer',
}

// Legacy type for backward compatibility with LLM API
export type ChatMessage = {
  role: Roles;
  content: string;
};

// New Firestore message type matching the coaching chat schema
export type FirestoreMessage = {
  id?: string;
  text: string;
  senderId: string;
  createdAt: Date | any; // Firestore Timestamp or Date
  type: 'user' | 'bot' | 'system';
  dedupeKey?: string;
};

// Message type for UI display (combines both schemas)
export type DisplayMessage = {
  id: string;
  text: string;
  type: 'user' | 'bot' | 'system';
  createdAt: Date;
  role?: Roles; // For backward compatibility
};

export type DietPreferences = {
  allergies: string[];
  diet: string;
  timeToCook: string;
  experience: ExperienceLevel;
  calories: number;
  goals: string;
  notes?: string; // Optional field for storing user modifications
};

export type UserFeedback = {
  id?: string; // Document ID when retrieved from Firestore
  feedback: string;
  category: 'general' | 'feature_request' | 'bug_report' | 'user_experience' | 'recipe_quality' | 'other';
  timestamp: Date;
};

export enum ExperienceLevel {
  beginner = 'beginner',
  intermediate = 'intermediate',
  experienced = 'experienced',
}
