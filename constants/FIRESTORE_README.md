# Firestore Collections Constants

## Overview

This file provides type-safe constants for all Firestore collection and subcollection names to prevent typos and maintain consistency across the codebase.

## Usage

### Import

```typescript
import { COLLECTIONS, SUBCOLLECTIONS, dm<PERSON><PERSON>ForBot, dm<PERSON><PERSON><PERSON>orUsers } from '@/repositories/firestoreRepository';
// or
import { COLLECTIONS, SUBCOLLECTIONS, dmKeyForBot, dmKeyForUsers } from '@/constants/FirestoreCollections';
// or use the convenience export
import { COLLECTIONS, SUBCOLLECTIONS, dmKeyForBot, dmKeyForUsers } from '@/constants';
```

### Root Collections

```typescript
// ❌ Don't use string literals
await firestoreRepository.getDocument('dietPreferences', userId);

// ✅ Use constants
await firestoreRepository.getDocument(COLLECTIONS.DIET_PREFERENCES, userId);
```

### Subcollections

```typescript
// ❌ Don't use string literals
await firestoreRepository.getSubcollection('users', userId, 'savedRecipes');

// ✅ Use constants
await firestoreRepository.getSubcollection(
  COLLECTIONS.USERS,
  userId,
  SUBCOLLECTIONS.USERS.SAVED_RECIPES
);
```

## Available Collections

### Root Collections
- `COLLECTIONS.DIET_PREFERENCES` - User dietary preferences
- `COLLECTIONS.CONVERSATIONS` - Chat conversations (for future use)
- `COLLECTIONS.CONVERSATION_KEYS` - DM uniqueness guard (for future use)
- `COLLECTIONS.INVENTORY` - User inventory items
- `COLLECTIONS.GROCERY_LIST` - User grocery lists
- `COLLECTIONS.GENERATED_RECIPES` - Generated recipes
- `COLLECTIONS.SAVED_RECIPES` - Saved recipes
- `COLLECTIONS.USERS` - User profiles

### Subcollections

#### Under `users/{uid}/`
- `SUBCOLLECTIONS.USERS.INBOX` - User inbox (for future use)
- `SUBCOLLECTIONS.USERS.PUSH_TOKENS` - Push notification tokens (for future use)
- `SUBCOLLECTIONS.USERS.SAVED_RECIPES` - User's saved recipe references
- `SUBCOLLECTIONS.USERS.USER_FEEDBACKS` - User feedback submissions

#### Under `generatedRecipes/{uid}/`
- `SUBCOLLECTIONS.GENERATED_RECIPES.RECIPES` - Individual recipe documents

#### Under `conversations/{conversationId}/` (for future use)
- `SUBCOLLECTIONS.CONVERSATIONS.PARTICIPANTS` - Conversation participants
- `SUBCOLLECTIONS.CONVERSATIONS.MESSAGES` - Conversation messages

## DM Key Helpers

### Generate DM key for ChefPal bot conversation

```typescript
const dmKey = dmKeyForBot(userId);
// Returns: "bot:chefpal|user:{userId}"

// Use it to find or create a bot conversation
const conversationKeyRef = doc(db, COLLECTIONS.CONVERSATION_KEYS, dmKey);
```

### Generate DM key for user-to-user conversation

```typescript
const dmKey = dmKeyForUsers(userIdA, userIdB);
// Returns: "user:{sortedIdA}|user:{sortedIdB}"
// The IDs are automatically sorted to ensure consistency

// Example:
dmKeyForUsers('user123', 'user456'); // "user:user123|user:user456"
dmKeyForUsers('user456', 'user123'); // "user:user123|user:user456" (same result)
```

## Examples

### Get a document
```typescript
const dietPrefs = await firestoreRepository.getDocument(
  COLLECTIONS.DIET_PREFERENCES,
  userId
);
```

### Add/Replace a document
```typescript
await firestoreRepository.addOrReplaceDocument(
  COLLECTIONS.INVENTORY,
  userId,
  { items: inventoryItems }
);
```

### Get a subcollection
```typescript
const savedRecipes = await firestoreRepository.getSubcollection(
  COLLECTIONS.USERS,
  userId,
  SUBCOLLECTIONS.USERS.SAVED_RECIPES
);
```

### Add to a subcollection
```typescript
await firestoreRepository.addSubcollectionDocument(
  COLLECTIONS.USERS,
  userId,
  SUBCOLLECTIONS.USERS.SAVED_RECIPES,
  recipeId,
  { savedAt: Timestamp.now() }
);
```

### Delete from a subcollection
```typescript
await firestoreRepository.deleteSubcollectionDocument(
  COLLECTIONS.USERS,
  userId,
  SUBCOLLECTIONS.USERS.SAVED_RECIPES,
  recipeId
);
```

## Benefits

1. **Type Safety** - Catch typos at compile time
2. **Autocomplete** - IDE suggests available collections
3. **Refactoring** - Easy to rename collections across the codebase
4. **Documentation** - Self-documenting code
5. **Consistency** - Single source of truth

## Adding New Collections

When adding a new collection or subcollection:

1. Add it to `constants/FirestoreCollections.ts`
2. Update the `COLLECTIONS` or `SUBCOLLECTIONS` object
3. If it's a root collection, add it to `firebase/firebaseConfig.ts`
4. Update this README with the new collection

## Files

- `constants/FirestoreCollections.ts` - Main constants file
- `firebase/firebaseConfig.ts` - Firebase collection references
- `repositories/firestoreRepository.ts` - Repository implementation

