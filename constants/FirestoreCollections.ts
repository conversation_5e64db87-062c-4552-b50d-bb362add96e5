/**
 * Firestore Collections and Subcollections Constants
 * 
 * This file provides type-safe constants for all Firestore collection and subcollection names
 * to prevent typos and maintain consistency across the codebase.
 */

/**
 * Root-level collection names
 */
export const COLLECTIONS = {
  DIET_PREFERENCES: 'dietPreferences',
  CONVERSATIONS: 'conversations',
  CONVERSATION_KEYS: 'conversationKeys',
  INVENTORY: 'inventory',
  GROCERY_LIST: 'groceryList',
  GENERATED_RECIPES: 'generatedRecipes',
  SAVED_RECIPES: 'savedRecipes',
  USERS: 'users',
} as const;

/**
 * Subcollection names organized by parent collection
 */
export const SUBCOLLECTIONS = {
  // Subcollections under /conversations/{conversationId}/
  CONVERSATIONS: {
    PARTICIPANTS: 'participants',
    MESSAGES: 'messages',
  },
  
  // Subcollections under /users/{uid}/
  USERS: {
    INBOX: 'inbox',
    PUSH_TOKENS: 'pushTokens',
    SAVED_RECIPES: 'savedRecipes',
    USER_FEEDBACKS: 'userFeedbacks',
  },
  
  // Subcollections under /generatedRecipes/{uid}/
  GENERATED_RECIPES: {
    RECIPES: 'recipes',
  },
} as const;

/**
 * Helper functions to build collection paths
 */
export const CollectionPaths = {
  // Root collections
  conversations: () => COLLECTIONS.CONVERSATIONS,
  conversationKeys: () => COLLECTIONS.CONVERSATION_KEYS,
  users: () => COLLECTIONS.USERS,
  dietPreferences: () => COLLECTIONS.DIET_PREFERENCES,
  inventory: () => COLLECTIONS.INVENTORY,
  groceryList: () => COLLECTIONS.GROCERY_LIST,
  generatedRecipes: () => COLLECTIONS.GENERATED_RECIPES,
  savedRecipes: () => COLLECTIONS.SAVED_RECIPES,

  // Conversation subcollections (for future use)
  conversationParticipants: (conversationId: string) =>
    `${COLLECTIONS.CONVERSATIONS}/${conversationId}/${SUBCOLLECTIONS.CONVERSATIONS.PARTICIPANTS}`,

  conversationMessages: (conversationId: string) =>
    `${COLLECTIONS.CONVERSATIONS}/${conversationId}/${SUBCOLLECTIONS.CONVERSATIONS.MESSAGES}`,

  // User subcollections (for future use)
  userInbox: (uid: string) =>
    `${COLLECTIONS.USERS}/${uid}/${SUBCOLLECTIONS.USERS.INBOX}`,

  userPushTokens: (uid: string) =>
    `${COLLECTIONS.USERS}/${uid}/${SUBCOLLECTIONS.USERS.PUSH_TOKENS}`,

  userSavedRecipes: (uid: string) =>
    `${COLLECTIONS.USERS}/${uid}/${SUBCOLLECTIONS.USERS.SAVED_RECIPES}`,

  userFeedbacks: (uid: string) =>
    `${COLLECTIONS.USERS}/${uid}/${SUBCOLLECTIONS.USERS.USER_FEEDBACKS}`,

  // Generated recipes subcollections
  userGeneratedRecipes: (uid: string) =>
    `${COLLECTIONS.GENERATED_RECIPES}/${uid}/${SUBCOLLECTIONS.GENERATED_RECIPES.RECIPES}`,
} as const;

/**
 * Helper functions for DM keys
 */

// dmKey for the ChefPal coach bot DM
export const dmKeyForBot = (uid: string) => `bot:coach|user:${uid}`;

// For user↔user DMs later, keep it sorted:
export const dmKeyForUsers = (a: string, b: string) => {
  const [x, y] = [a, b].sort();
  return `user:${x}|user:${y}`;
};

