# Timezone-Aware Coaching Messages - Changes Summary

## Overview
Modified the `send_daily_coaching_messages` Cloud Function to run every hour and check each user's timezone to determine if it's currently 3 PM for them before sending coaching messages.

## Files Modified

### 1. `functions/requirements.txt`
**Change:** Added `pytz` dependency for timezone handling

```diff
firebase_functions~=0.1.0
firebase_admin
requests
openai
+ pytz
```

### 2. `functions/main.py`
**Function:** `send_daily_coaching_messages`

#### Schedule Change
```diff
- @scheduler_fn.on_schedule(schedule="0 9 * * *", timezone="UTC", ...)
+ @scheduler_fn.on_schedule(schedule="0 * * * *", timezone="UTC", ...)
```
- **Before:** Ran daily at 9 AM UTC
- **After:** Runs every hour (on the hour)

#### Key Changes:
1. **Import pytz** for timezone handling
2. **Get current UTC time** at the start of each run
3. **For each user:**
   - Fetch timezone from `users/{uid}` document
   - Convert UTC time to user's local time
   - Check if it's 3 PM (15:00) in their timezone
   - Check if message already sent today (via `lastCoachingMessageDate`)
   - Only send message if all conditions are met
4. **Update user document** with `lastCoachingMessageDate` after sending
5. **Track skipped users** in logs

#### New Logic Flow:
```
Every Hour:
  For each user with diet preferences:
    1. Get user's timezone from Firestore
    2. Calculate user's local time
    3. Is it 3 PM? → No: Skip
    4. Already sent today? → Yes: Skip
    5. Send coaching message
    6. Update lastCoachingMessageDate
```

## Files Modified

### 3. `app/onboarding.tsx`
**Purpose:** Save user timezone during onboarding

**Changes:**
- Added import for `expo-localization`
- Modified `handleProceedToCamera` function to:
  - Get user's timezone using `Localization.timezone`
  - Save timezone to `users/{uid}` collection in parallel with diet preferences
  - Uses `Promise.all()` for efficient parallel writes

**Code:**
```typescript
import * as Localization from 'expo-localization';

// Get user's timezone
const timezone = Localization.timezone; // e.g. "America/Chicago"

// Save diet preferences and timezone in parallel
await Promise.all([
  firestoreRepository.addOrReplaceDocument(COLLECTIONS.DIET_PREFERENCES, user.uid, transformedPreferences),
  firestoreRepository.addOrReplaceDocument(COLLECTIONS.USERS, user.uid, { timezone, timezoneUpdatedAt: new Date() }),
]);
```

## Files Created

### 1. `functions/TIMEZONE_COACHING_MESSAGES.md`
**Purpose:** Detailed technical documentation

**Contents:**
- Overview of changes
- Schedule explanation
- Firestore schema requirements
- How it works (execution flow)
- Example scenarios
- Monitoring and logs
- Testing instructions
- Troubleshooting guide
- Future enhancements

### 2. `TIMEZONE_SETUP_INSTRUCTIONS.md`
**Purpose:** Step-by-step setup guide

**Contents:**
- Setup steps
- Cloud Function deployment
- User migration strategies
- Testing procedures
- Monitoring guidelines
- Troubleshooting
- Optional enhancements

### 3. `CHANGES_SUMMARY.md` (this file)
**Purpose:** Quick reference of all changes

## Firestore Schema Changes

### Users Collection
**New Fields:**
```typescript
{
  uid: string,
  lastActiveAt: Date,
  timezone: string,  // NEW: IANA timezone (e.g., "America/New_York")
  lastCoachingMessageDate?: string,  // NEW: Format "YYYY-MM-DD"
  timezoneUpdatedAt?: Date,  // NEW: When timezone was last updated
  // ... existing fields
}
```

## Required Actions

### Backend (Cloud Functions)
✅ **Completed:**
- [x] Updated function schedule to hourly
- [x] Added timezone checking logic
- [x] Added duplicate prevention
- [x] Added pytz dependency
- [x] Updated logging

⚠️ **To Do:**
- [ ] Install dependencies: `cd functions && pip install -r requirements.txt`
- [ ] Deploy function: `firebase deploy --only functions:send_daily_coaching_messages`

### Frontend (Mobile App)
✅ **Completed:**
- [x] Updated onboarding to save timezone
- [x] Uses expo-localization (already installed with Expo)
- [x] Saves timezone in parallel with diet preferences

⚠️ **To Do:**
- [ ] (Optional) Create timezone settings screen
- [ ] (Optional) Migrate existing users' timezones

## How It Works

### Example Scenario
**Current UTC Time:** 2025-10-06 19:00:00 UTC

| User | Timezone | Local Time | Hour | Action |
|------|----------|------------|------|--------|
| User A | America/New_York (UTC-4) | 15:00 EDT | 15 | ✅ **Send message** |
| User B | Europe/London (UTC+1) | 20:00 BST | 20 | ❌ Skip (not 3 PM) |
| User C | Asia/Tokyo (UTC+9) | 04:00 JST | 4 | ❌ Skip (not 3 PM) |
| User D | America/Los_Angeles (UTC-7) | 12:00 PDT | 12 | ❌ Skip (not 3 PM) |

**Next Hour (20:00 UTC):**
- User A: 16:00 EDT → Skip (not 3 PM)
- User B: 21:00 BST → Skip (not 3 PM)
- User C: 05:00 JST → Skip (not 3 PM)
- User D: 13:00 PDT → Skip (not 3 PM)

**Two Hours Later (22:00 UTC):**
- User D: 15:00 PDT → ✅ **Send message** (if not already sent today)

## Testing Checklist

### Backend Testing
- [ ] Deploy function successfully
- [ ] Check function runs every hour (view logs)
- [ ] Verify timezone detection works
- [ ] Confirm duplicate prevention works
- [ ] Test with invalid timezone (should skip)
- [ ] Test with missing timezone (should skip)

### Frontend Testing
- [ ] Install react-native-localize successfully
- [ ] Timezone detection works on device
- [ ] Timezone saves to Firestore correctly
- [ ] AuthContext integration works
- [ ] Timezone updates on app launch

### Integration Testing
- [ ] Set test user timezone to current hour = 15
- [ ] Wait for next hour trigger
- [ ] Verify message received
- [ ] Verify no duplicate message in same day
- [ ] Check logs for correct execution

## Monitoring

### Cloud Function Logs
Monitor in Firebase Console → Functions → send_daily_coaching_messages

**Key Metrics:**
- Execution frequency (should be every hour)
- Messages sent per hour
- Users skipped (and reasons)
- Errors or failures

**Sample Log Output:**
```
Starting hourly coaching message check at 2025-10-06 19:00:00+00:00
Generating coaching message for user abc123 (local time: 2025-10-06 15:00 EDT)
Successfully sent coaching message to user abc123 in conversation xyz789
No timezone set for user def456, skipping
Already sent coaching message to user ghi789 today (2025-10-06), skipping
Hourly coaching message check completed. Messages sent: 1, Users processed: 1, Failed: 0, Skipped: 2
```

## Deployment Steps

### 1. Install Backend Dependencies
```bash
cd functions
pip install -r requirements.txt
cd ..
```

### 2. Deploy Cloud Function
```bash
firebase deploy --only functions:send_daily_coaching_messages
```

### 3. Test
- Complete onboarding in app
- Check Firestore for timezone field in users collection
- Wait for 3 PM in your timezone
- Verify message received

## Rollback Plan

If issues occur, you can rollback:

### Rollback Cloud Function
```bash
# Revert to previous version
git revert <commit-hash>
firebase deploy --only functions:send_daily_coaching_messages
```

### Temporary Fix
Change schedule back to daily:
```python
@scheduler_fn.on_schedule(schedule="0 9 * * *", timezone="UTC", ...)
```

## Future Enhancements

### Phase 2 (Optional)
1. **Customizable Time:** Let users choose their preferred message time
2. **Frequency Control:** Daily, weekly, or custom frequency
3. **Timezone Settings UI:** Manual timezone selection screen
4. **Smart Timing:** ML-based optimal message timing
5. **Time Zone Auto-Update:** Detect timezone changes automatically

### Phase 3 (Advanced)
1. **Multi-timezone Support:** Handle users traveling across timezones
2. **Message Queuing:** Queue messages for offline users
3. **A/B Testing:** Test different message times for engagement
4. **Analytics:** Track message open rates by timezone

## Support & Documentation

- **Detailed Docs:** `functions/TIMEZONE_COACHING_MESSAGES.md`
- **Setup Guide:** `TIMEZONE_SETUP_INSTRUCTIONS.md`
- **Quick Reference:** `QUICK_REFERENCE.md`
- **This Summary:** `CHANGES_SUMMARY.md`

## Questions?

Common questions answered in documentation:
- How do I set a user's timezone? → See `TIMEZONE_SETUP_INSTRUCTIONS.md`
- Why isn't my user receiving messages? → See troubleshooting section
- How do I test this? → See testing checklist above
- Can users change their timezone? → Yes, use `saveUserTimezone()`
- What if timezone is invalid? → Function skips user and logs error

