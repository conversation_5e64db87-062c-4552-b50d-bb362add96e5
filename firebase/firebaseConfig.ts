import { getApp } from '@react-native-firebase/app';
import { getAuth } from '@react-native-firebase/auth';
import { getFirestore, collection } from '@react-native-firebase/firestore';

// Firebase is automatically initialized with react-native-firebase
// using the google-services.json and GoogleService-Info.plist files

// Get Firebase app instance - use the default app
export const app = getApp();

// Export auth instance using modular API
export const auth = getAuth(app);

// Export firestore instance using modular API
export const db = getFirestore(app);

// Import collection constants for consistency
import { COLLECTIONS } from '@/constants/FirestoreCollections';

// Export collection references using modular API
export const dietPreferenceCollection = collection(db, COLLECTIONS.DIET_PREFERENCES);
export const conversationCollection = collection(db, COLLECTIONS.CONVERSATIONS);
export const conversationKeysCollection = collection(db, COLLECTIONS.CONVERSATION_KEYS);
export const inventoryCollection = collection(db, COLLECTIONS.INVENTORY);
export const groceryListCollection = collection(db, COLLECTIONS.GROCERY_LIST);
export const generatedRecipesCollection = collection(db, COLLECTIONS.GENERATED_RECIPES);
export const usersCollection = collection(db, COLLECTIONS.USERS);
export const savedRecipesCollection = collection(db, COLLECTIONS.SAVED_RECIPES);
