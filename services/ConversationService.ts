import { getIdToken } from '@react-native-firebase/auth';
import { auth } from '@/firebase/firebaseConfig';
import firestore, { FirebaseFirestoreTypes } from '@react-native-firebase/firestore';
import { COLLECTIONS, SUBCOLLECTIONS } from '@/constants/FirestoreCollections';
import logger from '@/services/logger';

// Generate a simple unique ID (similar to ULID but simpler)
function generateMessageId(): string {
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substring(2, 15);
  return `${timestamp}${randomPart}`;
}

export type MessageType = 'bot' | 'user' | 'system';

export interface Message {
  id: string;
  text: string;
  senderId: string;
  createdAt: FirebaseFirestoreTypes.Timestamp | null;
  type: MessageType;
  dedupeKey: string;
}

export interface Conversation {
  id: string;
  type: string;
  dmKey?: string;
  createdAt: FirebaseFirestoreTypes.Timestamp;
  createdBy: string;
  lastMessage?: string;
  lastMessageAt?: FirebaseFirestoreTypes.Timestamp;
  lastSenderId?: string;
}

/**
 * Service for managing conversations and messages
 */
export class ConversationService {
  private static readonly CLOUD_FUNCTION_URL = 
    'https://us-central1-chefpal-a9abe.cloudfunctions.net/get_or_create_dm';

  /**
   * Get or create a DM conversation with the coach bot
   * @param uid - User ID
   * @returns Promise<string> - Conversation ID
   */
  static async getOrCreateCoachDM(uid: string): Promise<string> {
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('User not authenticated');
      }

      const idToken = await getIdToken(user);

      const response = await fetch(this.CLOUD_FUNCTION_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${idToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          uid,
          type: 'bot_dm',
          botId: 'coach',
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to get or create DM');
      }

      const data = await response.json();
      logger.info('Coach DM conversation created/retrieved', {
        additionalData: { conversationId: data.conversationId },
      });
      return data.conversationId as string;
    } catch (error) {
      logger.error('Error getting or creating coach DM', error as Error, {
        userId: uid,
      });
      throw error;
    }
  }

  /**
   * Send a message to a conversation
   * @param conversationId - Conversation ID
   * @param senderId - Sender ID (user ID or bot ID)
   * @param text - Message text
   * @param type - Message type (user, bot, or system)
   * @returns Promise<string> - Message ID
   */
  static async sendMessage(
    conversationId: string,
    senderId: string,
    text: string,
    type: MessageType = 'user'
  ): Promise<string> {
    try {
      const messageId = generateMessageId();
      const msgRef = firestore()
        .collection(COLLECTIONS.CONVERSATIONS)
        .doc(conversationId)
        .collection(SUBCOLLECTIONS.CONVERSATIONS.MESSAGES)
        .doc(messageId);

      await msgRef.set({
        text,
        senderId,
        createdAt: firestore.FieldValue.serverTimestamp(),
        type,
        dedupeKey: messageId,
      });

      logger.info('Message sent', {
        additionalData: { conversationId, messageId, type },
      });
      return messageId;
    } catch (error) {
      logger.error('Error sending message', error as Error, {
        additionalData: { conversationId, senderId },
      });
      throw error;
    }
  }

  /**
   * Get messages from a conversation with pagination
   * @param conversationId - Conversation ID
   * @param limitCount - Number of messages to fetch
   * @returns Promise<Message[]> - Array of messages
   */
  static async getMessages(
    conversationId: string,
    limitCount: number = 50
  ): Promise<Message[]> {
    try {
      const querySnapshot = await firestore()
        .collection(COLLECTIONS.CONVERSATIONS)
        .doc(conversationId)
        .collection(SUBCOLLECTIONS.CONVERSATIONS.MESSAGES)
        .orderBy('createdAt', 'desc')
        .limit(limitCount)
        .get();

      const messages: Message[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        messages.push({
          id: doc.id,
          text: data.text || '',
          senderId: data.senderId || '',
          createdAt: data.createdAt || null,
          type: data.type || 'user',
          dedupeKey: data.dedupeKey || doc.id,
        });
      });

      // Reverse to get chronological order (oldest first)
      return messages.reverse();
    } catch (error) {
      logger.error('Error getting messages', error as Error, {
        additionalData: { conversationId },
      });
      return [];
    }
  }

  /**
   * Subscribe to real-time messages in a conversation
   * @param conversationId - Conversation ID
   * @param callback - Callback function to handle new messages
   * @param limitCount - Number of messages to fetch
   * @returns Unsubscribe function
   */
  static subscribeToMessages(
    conversationId: string,
    callback: (messages: Message[]) => void,
    limitCount: number = 50
  ): () => void {
    try {
      const unsubscribe = firestore()
        .collection(COLLECTIONS.CONVERSATIONS)
        .doc(conversationId)
        .collection(SUBCOLLECTIONS.CONVERSATIONS.MESSAGES)
        .orderBy('createdAt', 'asc')
        .limit(limitCount)
        .onSnapshot(
          (querySnapshot) => {
            const messages: Message[] = [];
            querySnapshot.forEach((doc) => {
              const data = doc.data();
              messages.push({
                id: doc.id,
                text: data.text || '',
                senderId: data.senderId || '',
                createdAt: data.createdAt || null,
                type: data.type || 'user',
                dedupeKey: data.dedupeKey || doc.id,
              });
            });
            callback(messages);
          },
          (error) => {
            logger.error('Error in message subscription', error as Error, {
              additionalData: { conversationId },
            });
          }
        );

      return unsubscribe;
    } catch (error) {
      logger.error('Error subscribing to messages', error as Error, {
        additionalData: { conversationId },
      });
      return () => {};
    }
  }

  /**
   * Mark a conversation as read for a user
   * @param conversationId - Conversation ID
   * @param uid - User ID
   */
  static async markAsRead(conversationId: string, uid: string): Promise<void> {
    try {
      // Update participant document (create if doesn't exist)
      const participantRef = firestore()
        .collection(COLLECTIONS.CONVERSATIONS)
        .doc(conversationId)
        .collection(SUBCOLLECTIONS.CONVERSATIONS.PARTICIPANTS)
        .doc(uid);

      await participantRef.set(
        {
          lastReadAt: firestore.FieldValue.serverTimestamp(),
          unreadCount: 0,
        },
        { merge: true }
      );

      // Also update inbox (create if doesn't exist)
      const inboxRef = firestore()
        .collection(COLLECTIONS.USERS)
        .doc(uid)
        .collection(SUBCOLLECTIONS.USERS.INBOX)
        .doc(conversationId);

      await inboxRef.set(
        {
          unreadCount: 0,
        },
        { merge: true }
      );

      logger.info('Conversation marked as read', {
        additionalData: { conversationId, uid },
      });
    } catch (error) {
      logger.error('Error marking conversation as read', error as Error, {
        additionalData: { conversationId, uid },
      });
      // Don't throw - marking as read is not critical
      // Just log the error and continue
    }
  }

  /**
   * Get user's inbox (list of conversations)
   * @param uid - User ID
   * @param limitCount - Number of conversations to fetch
   * @returns Promise<any[]> - Array of inbox items
   */
  static async getInbox(uid: string, limitCount: number = 50): Promise<any[]> {
    try {
      const querySnapshot = await firestore()
        .collection(COLLECTIONS.USERS)
        .doc(uid)
        .collection(SUBCOLLECTIONS.USERS.INBOX)
        .orderBy('lastMessageAt', 'desc')
        .limit(limitCount)
        .get();

      const inbox: any[] = [];

      querySnapshot.forEach((doc) => {
        inbox.push({
          id: doc.id,
          ...doc.data(),
        });
      });

      return inbox;
    } catch (error) {
      logger.error('Error getting inbox', error as Error, {
        userId: uid,
      });
      return [];
    }
  }
}

