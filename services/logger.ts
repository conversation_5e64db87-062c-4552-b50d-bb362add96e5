/**
 * Unified Logging Service
 * A tiny wrapper that fans out to multiple transports:
 * - <PERSON>sole (dev only, pretty printing)
 * - Remote error service in prod (Crashlytics)
 */

import crashlyticsService from './CrashlyticsService';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface LogContext {
  userId?: string;
  screen?: string;
  action?: string;
  apiEndpoint?: string;
  httpStatus?: number;
  additionalData?: Record<string, any>;
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  context?: LogContext;
  error?: Error;
  timestamp: Date;
}

class Logger {
  private isDev = __DEV__;
  private minLogLevel = this.isDev ? LogLevel.DEBUG : LogLevel.INFO;

  /**
   * Debug logging - development only
   */
  debug(message: string, context?: LogContext): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  /**
   * Info logging
   */
  info(message: string, context?: LogContext): void {
    this.log(LogLevel.INFO, message, context);
  }

  /**
   * Warning logging
   */
  warn(message: string, context?: LogContext): void {
    this.log(LogLevel.WARN, message, context);
  }

  /**
   * Error logging with optional Error object
   */
  error(message: string, error?: Error, context?: LogContext): void {
    this.log(LogLevel.ERROR, message, context, error);
  }

  /**
   * API error logging with structured context
   */
  apiError(
    endpoint: string,
    httpStatus: number,
    message: string,
    context?: Partial<LogContext>
  ): void {
    const apiContext: LogContext = {
      ...context,
      apiEndpoint: endpoint,
      httpStatus,
      action: 'api_call',
    };
    this.error(`API Error: ${message} (${httpStatus}) at ${endpoint}`, undefined, apiContext);
  }

  /**
   * Network error logging
   */
  networkError(endpoint: string, message: string, context?: Partial<LogContext>): void {
    const networkContext: LogContext = {
      ...context,
      apiEndpoint: endpoint,
      action: 'network_request',
    };
    this.error(`Network Error: ${message} at ${endpoint}`, undefined, networkContext);
  }

  /**
   * Authentication error logging
   */
  authError(message: string, context?: Partial<LogContext>): void {
    this.error(`Auth Error: ${message}`, undefined, context);
  }

  /**
   * Data processing error logging
   */
  dataError(operation: string, message: string, context?: Partial<LogContext>): void {
    const dataContext: LogContext = {
      ...context,
      action: operation,
    };
    this.error(`Data Processing Error: ${message} during ${operation}`, undefined, dataContext);
  }

  /**
   * UI error logging
   */
  uiError(screen: string, message: string, context?: Partial<LogContext>): void {
    const uiContext: LogContext = {
      ...context,
      screen,
      action: 'ui_interaction',
    };
    this.error(`UI Error: ${message} on ${screen}`, undefined, uiContext);
  }

  /**
   * Core logging method that fans out to transports
   */
  private log(level: LogLevel, message: string, context?: LogContext, error?: Error): void {
    if (level < this.minLogLevel) {
      return;
    }

    const logEntry: LogEntry = {
      level,
      message,
      context,
      error,
      timestamp: new Date(),
    };

    // Transport 1: Console (dev only, pretty printing)
    this.logToConsole(logEntry);

    // Transport 2: Remote error service (prod)
    this.logToRemote(logEntry);
  }

  /**
   * Console transport with pretty printing
   */
  private logToConsole(entry: LogEntry): void {
    if (!this.isDev) return;

    const timestamp = entry.timestamp.toISOString();
    const levelName = LogLevel[entry.level];
    const prefix = `[${timestamp}] ${levelName}:`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(prefix, entry.message, entry.context || '');
        break;
      case LogLevel.INFO:
        console.info(prefix, entry.message, entry.context || '');
        break;
      case LogLevel.WARN:
        console.warn(prefix, entry.message, entry.context || '');
        break;
      case LogLevel.ERROR:
        console.error(prefix, entry.message);
        if (entry.error) {
          console.error('Error details:', entry.error);
        }
        if (entry.context) {
          console.error('Context:', entry.context);
        }
        break;
    }
  }

  /**
   * Remote transport (Crashlytics)
   */
  private async logToRemote(entry: LogEntry): Promise<void> {
    try {
      // Only send warnings and errors to remote service
      if (entry.level < LogLevel.WARN) {
        return;
      }

      // Log to Crashlytics with appropriate method
      if (entry.level === LogLevel.ERROR) {
        if (entry.error) {
          await crashlyticsService.recordError(entry.error, entry.context);
        } else {
          await crashlyticsService.logError(entry.message, 'GENERAL', entry.context);
        }
      } else if (entry.level === LogLevel.WARN) {
        await crashlyticsService.log(`WARNING: ${entry.message}`);
      }
    } catch (remoteError) {
      // Fallback to console if remote logging fails
      if (this.isDev) {
        console.error('Failed to log to remote service:', remoteError);
      }
    }
  }

  /**
   * Set user context for all subsequent logs
   */
  async setUserId(userId: string): Promise<void> {
    try {
      await crashlyticsService.setUserId(userId);
    } catch (error) {
      if (this.isDev) {
        console.error('Failed to set user ID in logger:', error);
      }
    }
  }

  /**
   * Set custom attributes for crash reports
   */
  async setAttributes(attributes: Record<string, string>): Promise<void> {
    try {
      await crashlyticsService.setAttributes(attributes);
    } catch (error) {
      if (this.isDev) {
        console.error('Failed to set attributes in logger:', error);
      }
    }
  }

  /**
   * Log a custom message for debugging
   */
  async logMessage(message: string): Promise<void> {
    try {
      await crashlyticsService.log(message);
      if (this.isDev) {
        console.log('Crashlytics log:', message);
      }
    } catch (error) {
      if (this.isDev) {
        console.error('Failed to log message:', error);
      }
    }
  }
}

// Export singleton instance
export const logger = new Logger();
export default logger;
