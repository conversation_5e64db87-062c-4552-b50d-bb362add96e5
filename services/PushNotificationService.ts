import messaging from '@react-native-firebase/messaging';
import { Platform, PermissionsAndroid } from 'react-native';
import { auth } from '@/firebase/firebaseConfig';
import firestore from '@react-native-firebase/firestore';
import { COLLECTIONS, SUBCOLLECTIONS } from '@/constants/FirestoreCollections';
import logger from '@/services/logger';

/**
 * Service for managing push notifications
 */
export class PushNotificationService {
  private static hasRequestedPermission = false;

  /**
   * Request notification permissions from the user
   * @returns Promise<boolean> - True if permission granted
   */
  static async requestPermission(): Promise<boolean> {
    try {
      if (this.hasRequestedPermission) {
        // Check current status without re-requesting
        const authStatus = await messaging().hasPermission();
        return authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
               authStatus === messaging.AuthorizationStatus.PROVISIONAL;
      }

      if (Platform.OS === 'android') {
        // Android 13+ requires runtime permission
        if (Platform.Version >= 33) {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
          );
          if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
            logger.info('Android notification permission denied');
            return false;
          }
        }
      }

      const authStatus = await messaging().requestPermission();
      this.hasRequestedPermission = true;

      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        logger.info('Notification permission granted', {
          additionalData: { authStatus },
        });
      } else {
        logger.info('Notification permission denied', {
          additionalData: { authStatus },
        });
      }

      return enabled;
    } catch (error) {
      logger.error('Error requesting notification permission', error as Error);
      return false;
    }
  }

  /**
   * Get the FCM token for this device
   * @returns Promise<string | null> - FCM token or null if unavailable
   */
  static async getToken(): Promise<string | null> {
    try {
      const hasPermission = await this.requestPermission();
      if (!hasPermission) {
        logger.info('Cannot get FCM token: permission not granted');
        return null;
      }

      const token = await messaging().getToken();
      logger.info('FCM token retrieved', {
        additionalData: { tokenLength: token?.length },
      });
      return token;
    } catch (error) {
      logger.error('Error getting FCM token', error as Error);
      return null;
    }
  }

  /**
   * Register the device token with Firestore
   * @param uid - User ID
   * @returns Promise<boolean> - True if successful
   */
  static async registerToken(uid: string): Promise<boolean> {
    try {
      const token = await this.getToken();
      if (!token) {
        logger.info('No FCM token to register');
        return false;
      }

      // Use token as document ID to prevent duplicates
      const tokenRef = firestore()
        .collection(COLLECTIONS.USERS)
        .doc(uid)
        .collection(SUBCOLLECTIONS.USERS.PUSH_TOKENS)
        .doc(token);

      await tokenRef.set({
        token,
        platform: Platform.OS,
        appVersion: '1.0.0', // TODO: Get from app config
        createdAt: firestore.FieldValue.serverTimestamp(),
        lastSeenAt: firestore.FieldValue.serverTimestamp(),
      });

      logger.info('FCM token registered', {
        userId: uid,
        additionalData: { platform: Platform.OS },
      });
      return true;
    } catch (error) {
      logger.error('Error registering FCM token', error as Error, {
        userId: uid,
      });
      return false;
    }
  }

  /**
   * Unregister the device token from Firestore
   * @param uid - User ID
   * @returns Promise<boolean> - True if successful
   */
  static async unregisterToken(uid: string): Promise<boolean> {
    try {
      const token = await messaging().getToken();
      if (!token) {
        logger.info('No FCM token to unregister');
        return false;
      }

      const tokenRef = firestore()
        .collection(COLLECTIONS.USERS)
        .doc(uid)
        .collection(SUBCOLLECTIONS.USERS.PUSH_TOKENS)
        .doc(token);

      await tokenRef.delete();
      logger.info('FCM token unregistered', { userId: uid });
      return true;
    } catch (error) {
      logger.error('Error unregistering FCM token', error as Error, {
        userId: uid,
      });
      return false;
    }
  }

  /**
   * Clean up old tokens for a user (tokens not seen in 30+ days)
   * @param uid - User ID
   */
  static async cleanupOldTokens(uid: string): Promise<void> {
    try {
      const tokensRef = firestore()
        .collection(COLLECTIONS.USERS)
        .doc(uid)
        .collection(SUBCOLLECTIONS.USERS.PUSH_TOKENS);

      const snapshot = await tokensRef.get();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      let deletedCount = 0;
      const deletePromises: Promise<void>[] = [];

      snapshot.forEach((doc) => {
        const data = doc.data();
        const lastSeenAt = data.lastSeenAt?.toDate();

        if (lastSeenAt && lastSeenAt < thirtyDaysAgo) {
          deletePromises.push(doc.ref.delete());
          deletedCount++;
        }
      });

      await Promise.all(deletePromises);

      if (deletedCount > 0) {
        logger.info('Cleaned up old FCM tokens', {
          userId: uid,
          additionalData: { deletedCount },
        });
      }
    } catch (error) {
      logger.error('Error cleaning up old tokens', error as Error, {
        userId: uid,
      });
    }
  }

  /**
   * Initialize push notifications for the current user
   * Sets up token registration and refresh handling
   */
  static async initialize(): Promise<void> {
    try {
      const user = auth.currentUser;
      if (!user) {
        logger.info('Cannot initialize push notifications: no user');
        return;
      }

      // Register current token
      await this.registerToken(user.uid);

      // Clean up old tokens
      await this.cleanupOldTokens(user.uid);

      // Listen for token refresh
      messaging().onTokenRefresh(async () => {
        logger.info('FCM token refreshed');
        await this.registerToken(user.uid);
      });
    } catch (error) {
      logger.error('Error initializing push notifications', error as Error);
    }
  }

  /**
   * Handle foreground notifications
   * @param handler - Function to call when notification is received
   * @returns Unsubscribe function
   */
  static onForegroundMessage(
    handler: (message: any) => void
  ): () => void {
    return messaging().onMessage(async (remoteMessage) => {
      logger.info('Foreground notification received', {
        additionalData: {
          messageId: remoteMessage.messageId,
          notification: remoteMessage.notification?.title,
        },
      });
      handler(remoteMessage);
    });
  }

  /**
   * Handle notification tap when app is in background
   * @param handler - Function to call when notification is tapped
   * @returns Unsubscribe function
   */
  static onNotificationOpenedApp(
    handler: (message: any) => void
  ): () => void {
    return messaging().onNotificationOpenedApp((remoteMessage) => {
      logger.info('Notification opened app from background', {
        additionalData: {
          messageId: remoteMessage.messageId,
          data: remoteMessage.data,
        },
      });
      handler(remoteMessage);
    });
  }

  /**
   * Get the notification that opened the app (if any)
   * Call this on app startup
   * @returns Promise<any | null> - Remote message or null
   */
  static async getInitialNotification(): Promise<any | null> {
    try {
      const remoteMessage = await messaging().getInitialNotification();
      if (remoteMessage) {
        logger.info('App opened from notification', {
          additionalData: {
            messageId: remoteMessage.messageId,
            data: remoteMessage.data,
          },
        });
      }
      return remoteMessage;
    } catch (error) {
      logger.error('Error getting initial notification', error as Error);
      return null;
    }
  }

  /**
   * Set badge count (iOS only)
   * Note: Badge count is managed by APNs payload in notifications
   * This method is kept for manual badge management if needed
   * @param count - Badge count
   */
  static async setBadgeCount(count: number): Promise<void> {
    if (Platform.OS === 'ios') {
      try {
        // Note: react-native-firebase/messaging doesn't have badge methods
        // Badge is set via APNs payload in the notification
        // For manual badge management, you'd need @react-native-community/push-notification-ios
        logger.info('Badge count set via notification payload', {
          additionalData: { count },
        });
      } catch (error) {
        logger.error('Error setting badge count', error as Error);
      }
    }
  }

  /**
   * Get current badge count (iOS only)
   * Note: Badge count is managed by APNs payload in notifications
   * @returns Promise<number> - Current badge count (always 0 for now)
   */
  static async getBadgeCount(): Promise<number> {
    // Note: react-native-firebase/messaging doesn't have badge methods
    // For badge management, you'd need @react-native-community/push-notification-ios
    return 0;
  }
}

