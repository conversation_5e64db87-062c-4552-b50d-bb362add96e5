import { Ingredient } from '@/components/types';
import { LlmService } from '@/services/LlmService';
import logger from '@/services/logger';
import { Roles } from '@/constants/Types';
import { imageAnalysisSchema } from '@/schemas/imageAnalysis';
import { imageAnalysisModel } from '@/constants/LlmConfigs';

export async function analyzeImagesAsync(imageUris: string[]): Promise<Ingredient[]> {
  try {
    if (imageUris.length === 0) {
      return [];
    }

    // Convert image URIs to base64
    const imagePromises = imageUris.map(async (uri) => {
      const response = await fetch(uri);
      const blob = await response.blob();
      return new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64 = reader.result as string;
          resolve(base64.split(',')[1]); // Remove the data URL prefix
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    });

    const base64Images = await Promise.all(imagePromises);
    const instructions = `You are a helpful assistant that identifies food items in images, such as a picture of inside a fridge, pantry, or grocery receipt. List all visible food items.
    If one food item (i.e. chocolate) contains multiple ingredients (i.e. Vanilla, orange, cashew butter), respond with the main ingredient (i.e. "Vanilla orange chocolate").
    Return your response as a JSON array of ingredient names with no other text or formatting.`;

    // Process each image individually to ensure all are analyzed
    const allIngredients: Ingredient[] = [];

    const responses = await Promise.all(
      base64Images.map((base64Image) =>
        LlmService.callLlmApi(
          imageAnalysisModel,
          instructions,
          [
            {
              role: Roles.user,
              content: [
                {
                  type: 'input_text',
                  text: 'What food items are visible in this image? The first letter of each ingredient should be capitalized. If you cannot identify any, respond <NOT FOUND>.',
                },
                {
                  type: 'input_image',
                  detail: 'auto',
                  image_url: `data:image/jpeg;base64,${base64Image}`,
                },
              ],
            },
          ],
          imageAnalysisSchema,
          'ingredients'
        )
      )
    );

    for (const response of responses) {
      const outputText = LlmService.extractOutputText(response);
      if (!outputText || outputText.includes('NOT FOUND')) continue;

      try {
        const parsed = JSON.parse(outputText);

        if (!parsed.ingredients || !Array.isArray(parsed.ingredients)) {
          logger.warn('Invalid response format from image analysis');
          continue;
        }

        for (const name of parsed.ingredients) {
          logger.debug(`Ingredient: ${name}`);
          if (
            typeof name === 'string' &&
            name.trim() &&
            !allIngredients.some((ingredient) => ingredient.name.toLowerCase() === name.toLowerCase())
          ) {
            allIngredients.push({
              name,
              // availability will be computed dynamically based on inventory
            });
          }
        }
      } catch (err) {
        logger.warn('Error parsing image analysis response', { additionalData: { error: err } });
      }
    }

    return allIngredients;
  } catch (error) {
    logger.dataError('image_analysis', error instanceof Error ? error.message : 'Unknown error');
    throw error;
  }
}
