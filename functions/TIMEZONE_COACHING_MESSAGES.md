# Timezone-Aware Coaching Messages

## Overview

The `send_daily_coaching_messages` Cloud Function has been updated to run every hour and check each user's timezone to determine if it's currently 3 PM for them. If so, it sends a personalized coaching message.

## Changes Made

### 1. Schedule Change
- **Before**: Ran daily at 9 AM UTC (`schedule="0 9 * * *"`)
- **After**: Runs every hour (`schedule="0 * * * *"`)

### 2. Timezone-Aware Logic
The function now:
1. Runs every hour on the hour
2. For each user with diet preferences:
   - Fetches their timezone from the `users` collection
   - Converts current UTC time to the user's local time
   - Checks if it's currently 3 PM (15:00) in their timezone
   - Verifies that a message hasn't already been sent today
   - If all conditions are met, generates and sends a coaching message

### 3. Duplicate Prevention
- Tracks the last coaching message date in the user document (`lastCoachingMessageDate` field)
- Prevents sending multiple messages on the same day even if the function runs multiple times during the 3 PM hour

### 4. Dependencies
- Added `pytz` to `requirements.txt` for timezone handling

## Firestore Schema Requirements

### Users Collection
Each user document in the `users` collection must have a `timezone` field:

```typescript
{
  uid: string,
  lastActiveAt: Date,
  timezone: string,  // IANA timezone string (e.g., "America/New_York", "Europe/London", "Asia/Tokyo")
  lastCoachingMessageDate?: string,  // Format: "YYYY-MM-DD", automatically set by the function
  // ... other fields
}
```

### Valid Timezone Values
The `timezone` field should contain a valid IANA timezone string. Examples:
- `"America/New_York"` (Eastern Time)
- `"America/Los_Angeles"` (Pacific Time)
- `"America/Chicago"` (Central Time)
- `"America/Denver"` (Mountain Time)
- `"Europe/London"` (GMT/BST)
- `"Europe/Paris"` (CET/CEST)
- `"Asia/Tokyo"` (JST)
- `"Australia/Sydney"` (AEST/AEDT)

Full list: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones

## Implementation Steps

### 1. Update User Documents
You need to add the `timezone` field to existing user documents. This can be done:

#### Option A: During User Onboarding (Recommended)
✅ **Already Implemented** in `app/onboarding.tsx`

The timezone is automatically detected and saved when users complete onboarding:

```typescript
import * as Localization from 'expo-localization';
import { doc, setDoc } from '@react-native-firebase/firestore';
import { db } from '@/firebase/firebaseConfig';

// Get user's timezone
const timezone = Localization.timezone; // e.g., "America/Chicago"

// Save to Firestore (done in parallel with diet preferences)
await setDoc(doc(db, 'users', uid), {
  timezone,
  timezoneUpdatedAt: new Date(),
}, { merge: true });
```

#### Option B: Manual Update for Existing Users
For existing users without a timezone, manually set a default timezone in Firestore Console or use Firebase Admin SDK.

### 2. Install Dependencies
The function requires `pytz` for timezone handling. This has been added to `requirements.txt`.

To install:
```bash
cd functions
pip install -r requirements.txt
```

### 3. Deploy the Function
Deploy the updated function to Firebase:
```bash
firebase deploy --only functions:send_daily_coaching_messages
```

## How It Works

### Execution Flow
1. **Every Hour**: The function runs at the top of every hour (e.g., 1:00, 2:00, 3:00, etc.)
2. **User Iteration**: For each user with diet preferences:
   - Fetch user document from `users` collection
   - Check if `timezone` field exists
   - Convert current UTC time to user's local time
   - Check if local hour is 15 (3 PM)
3. **Duplicate Check**: If it's 3 PM:
   - Check `lastCoachingMessageDate` field
   - If it matches today's date, skip (already sent)
4. **Message Generation**: If all checks pass:
   - Get or create coach DM conversation
   - Fetch recent coaching messages for context
   - Generate personalized coaching tip using OpenAI
   - Send message to conversation
   - Update `lastCoachingMessageDate` to today's date

### Example Scenario
- Current UTC time: `2025-10-06 19:00:00 UTC`
- User A timezone: `America/New_York` (UTC-4)
  - User A local time: `2025-10-06 15:00:00 EDT` ✅ **3 PM - Message sent**
- User B timezone: `Europe/London` (UTC+1)
  - User B local time: `2025-10-06 20:00:00 BST` ❌ **8 PM - Skipped**
- User C timezone: `Asia/Tokyo` (UTC+9)
  - User C local time: `2025-10-07 04:00:00 JST` ❌ **4 AM - Skipped**

## Monitoring and Logs

The function logs detailed information:
- `Starting hourly coaching message check at {UTC_TIME}`
- `Generating coaching message for user {uid} (local time: {LOCAL_TIME})`
- `Already sent coaching message to user {uid} today ({DATE}), skipping`
- `No timezone set for user {uid}, skipping`
- `Hourly coaching message check completed. Messages sent: X, Users processed: Y, Failed: Z, Skipped: W`

## Testing

### Test Locally
You can test the timezone logic locally:

```python
from datetime import datetime
import pytz

# Simulate current UTC time
current_utc_time = datetime.now(pytz.UTC)

# Test user timezone
user_timezone = pytz.timezone("America/New_York")
user_local_time = current_utc_time.astimezone(user_timezone)

print(f"UTC Time: {current_utc_time}")
print(f"User Local Time: {user_local_time}")
print(f"User Hour: {user_local_time.hour}")
print(f"Is 3 PM? {user_local_time.hour == 15}")
```

### Test in Production
1. Set your user's timezone to a timezone where it's currently 3 PM
2. Wait for the next hour to trigger
3. Check Cloud Function logs for execution details
4. Verify you receive a coaching message

## Troubleshooting

### User Not Receiving Messages
1. **Check timezone field**: Ensure the user document has a valid `timezone` field
2. **Check time**: Verify it's actually 3 PM in the user's timezone
3. **Check duplicate prevention**: Look for `lastCoachingMessageDate` field - if it matches today, the message was already sent
4. **Check logs**: Review Cloud Function logs for errors or skip messages

### Invalid Timezone Error
If you see `Invalid timezone '{timezone}' for user {uid}, skipping`:
- The timezone string is not a valid IANA timezone
- Update the user document with a valid timezone from the list above

### Messages Sent Multiple Times
If users receive multiple messages in one day:
- Check that `lastCoachingMessageDate` is being updated correctly
- Verify the date format is consistent (`YYYY-MM-DD`)

## Future Enhancements

Possible improvements:
1. **Customizable Time**: Allow users to set their preferred coaching message time (not just 3 PM)
2. **Frequency Control**: Let users choose daily, weekly, or custom frequency
3. **Timezone Auto-Detection**: Automatically detect and update timezone based on user's device
4. **Retry Logic**: Retry failed messages with exponential backoff
5. **Message Scheduling**: Queue messages for delivery at optimal times based on user engagement patterns

