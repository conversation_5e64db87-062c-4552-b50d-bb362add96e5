# Get or Create DM Function

## Overview

The `get_or_create_dm` Cloud Function ensures a single DM conversation exists for a given `dmKey` using a Firestore transaction. This prevents duplicate conversations and provides idempotent conversation creation.

## Function Details

- **Type**: HTTP Cloud Function
- **Method**: POST only
- **Authentication**: Required (Firebase ID token)
- **Timeout**: 60 seconds
- **Endpoint**: `/get_or_create_dm`

## Request Format

### Headers
```
Authorization: Bearer <firebase_id_token>
Content-Type: application/json
```

### Body
```json
{
  "uid": "user123",
  "type": "bot_dm",
  "botId": "coach"
}
```

### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `uid` | string | Yes | - | The user ID for the conversation |
| `type` | string | No | `"bot_dm"` | Type of DM (currently only `"bot_dm"` supported) |
| `botId` | string | No | `"coach"` | Bot identifier (e.g., "coach", "chefpal") |

## Response Format

### Success (200)
```json
{
  "conversationId": "abc123xyz"
}
```

### Error Responses

#### 400 - Bad Request
```json
{
  "error": "uid required"
}
```
or
```json
{
  "error": "unsupported type for this endpoint"
}
```

#### 401 - Unauthorized
```json
{
  "error": "Authorization header required"
}
```
or
```json
{
  "error": "Invalid authentication token"
}
```

#### 403 - Forbidden
```json
{
  "error": "Unauthorized: uid mismatch"
}
```

#### 405 - Method Not Allowed
```json
{
  "error": "POST only"
}
```

#### 500 - Internal Server Error
```json
{
  "error": "Internal server error: <error details>"
}
```

## What It Does

1. **Validates Authentication**: Verifies the Firebase ID token and ensures the authenticated user matches the requested `uid`
2. **Builds DM Key**: Creates a deterministic key in the format `bot:{botId}|user:{uid}`
3. **Transaction Safety**: Uses a Firestore transaction to ensure atomicity
4. **Checks Existing**: If a conversation already exists for the `dmKey`, returns its ID
5. **Creates New**: If no conversation exists, creates:
   - `/conversations/{conversationId}` document with metadata
   - `/conversations/{conversationId}/participants/{uid}` for the user
   - `/conversations/{conversationId}/participants/{botId}` for the bot
   - `/conversationKeys/{dmKey}` as a uniqueness guard

## Firestore Structure Created

### `/conversations/{conversationId}`
```javascript
{
  type: "bot_dm",
  dmKey: "bot:coach|user:user123",
  createdAt: <SERVER_TIMESTAMP>,
  createdBy: "coach",
  // lastMessage, lastMessageAt, lastSenderId are populated by message trigger
}
```

### `/conversations/{conversationId}/participants/{uid}`
```javascript
{
  role: "user",
  lastReadAt: <SERVER_TIMESTAMP>,
  unreadCount: 0,
  muted: false,
  notifications: "all"
}
```

### `/conversations/{conversationId}/participants/{botId}`
```javascript
{
  role: "bot",
  unreadCount: 0,
  muted: false,
  notifications: "none"
}
```

### `/conversationKeys/{dmKey}`
```javascript
{
  conversationId: "abc123xyz",
  type: "bot_dm",
  createdAt: <SERVER_TIMESTAMP>
}
```

## Usage Example (Client-Side)

```typescript
import { auth } from '@/firebase/firebaseConfig';
import { dmKeyForBot } from '@/constants/FirestoreCollections';

async function getOrCreateCoachDM(userId: string): Promise<string> {
  const user = auth().currentUser;
  if (!user) throw new Error('Not authenticated');
  
  const idToken = await user.getIdToken();
  
  const response = await fetch(
    'https://us-central1-<project-id>.cloudfunctions.net/get_or_create_dm',
    {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${idToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        uid: userId,
        type: 'bot_dm',
        botId: 'coach',
      }),
    }
  );
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to get or create DM');
  }
  
  const data = await response.json();
  return data.conversationId;
}
```

## Transaction Guarantees

- **Atomicity**: All writes (conversation, participants, key guard) succeed or fail together
- **Idempotency**: Multiple calls with the same `dmKey` return the same `conversationId`
- **Race Condition Safety**: Concurrent calls won't create duplicate conversations
- **Uniqueness**: The `conversationKeys` collection acts as a guard against duplicates

## Security

- ✅ Requires Firebase authentication
- ✅ Verifies the authenticated user matches the requested `uid`
- ✅ Only allows POST requests
- ✅ Validates all required parameters
- ✅ Returns appropriate error codes

## Notes

- The `lastMessage`, `lastMessageAt`, and `lastSenderId` fields on the conversation document are intentionally left empty and will be populated by a message trigger when the first message is sent
- The bot participant has `notifications: "none"` to prevent unnecessary notifications
- The user participant has `notifications: "all"` by default
- The function is idempotent - calling it multiple times with the same parameters returns the same result

## Related Files

- `constants/FirestoreCollections.ts` - Contains `dmKeyForBot()` helper
- `functions/main.py` - Implementation

