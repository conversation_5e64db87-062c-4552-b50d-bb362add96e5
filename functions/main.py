import threading
from firebase_admin import initialize_app, get_app, auth as admin_auth, firestore, messaging
from firebase_functions import https_fn, firestore_fn, scheduler_fn
from firebase_functions.https_fn import Request, Response
from openai import OpenAI
import os
import json
import requests
from urllib.parse import quote

# Initialize Firebase Admin SDK (for Firestore/Auth/etc. if needed)
_init_lock = threading.Lock()
_app = None
_db  = None
def ensure_app():
    global _app
    if _app is None:
        with _init_lock:
            if _app is None:
                # If another thread initialised while we waited, get_app() succeeds
                try:
                    _app = get_app()
                except ValueError:              # not initialised yet
                    _app = initialize_app()

def get_db():
    global _db
    ensure_app()
    if _db is None:
        with _init_lock:
            if _db is None:
                _db = firestore.client()
    return _db

@https_fn.on_request(secrets=["OPENAI_API_KEY"], timeout_sec=300)
def generate(req: Request) -> Response:
    ensure_app()
    id_token = req.headers.get("Authorization", "").replace("Bearer ", "")
    decoded_token = admin_auth.verify_id_token(id_token)
    uid = decoded_token["uid"]

    try:
        data = req.get_json(silent=True)

        if not data or "input" not in data:
            return Response(json.dumps({"error": "Missing 'input' in request body"}), status=400, headers={"Content-Type": "application/json"})

        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            return Response(json.dumps({"error": "OpenAI API key not found"}), status=500, headers={"Content-Type": "application/json"})

        client = OpenAI(api_key=api_key)

        # Create parameters dictionary with required fields
        params = {
            "model": data.get("model", "gpt-5-mini"),
            "input": data["input"],
            "instructions": data.get("instructions"),
            "text": data.get("text", {"format": {"type": "text"}}),
            "user": uid
        }

        # Add previous_response_id if it exists in the request
        if "previous_response_id" in data and data["previous_response_id"]:
            params["previous_response_id"] = data["previous_response_id"]

        response = client.responses.create(**params)

        return Response(json.dumps(response.model_dump()), status=200, headers={"Content-Type": "application/json"})

    except Exception as e:
        return Response(json.dumps({"error": str(e)}), status=500, headers={"Content-Type": "application/json"})


@https_fn.on_request(secrets=["UNSPLASH_ACCESS_KEY"], timeout_sec=10)
def get_unsplash_image(req: Request) -> Response:
    ensure_app()
    # Verify authentication
    id_token = req.headers.get("Authorization", "").replace("Bearer ", "")
    try:
        decoded_token = admin_auth.verify_id_token(id_token)
        _ = decoded_token["uid"]
    except Exception as e:
        return Response(
            json.dumps({"error": "Unauthorized"}),
            status=401,
            headers={"Content-Type": "application/json"}
        )

    if req.method != "GET":
        return Response(
            json.dumps({"error": "Method not allowed. Only GET requests are supported."}),
            status=405,
            headers={"Content-Type": "application/json"}
        )

    try:
        query = req.args.get("query")

        if not query:
            return Response(
                json.dumps({"error": "Missing 'query' parameter"}),
                status=400,
                headers={"Content-Type": "application/json"}
            )

        # Get Unsplash API key from secrets
        unsplash_key = os.environ.get("UNSPLASH_ACCESS_KEY")
        if not unsplash_key:
            return Response(
                json.dumps({"error": "Unsplash API key not found"}),
                status=500,
                headers={"Content-Type": "application/json"}
            )

        # Call Unsplash API
        url = f"https://api.unsplash.com/search/photos?query={quote(query)}&per_page=1"
        headers = {
            "Authorization": f"Client-ID {unsplash_key}",
        }

        response = requests.get(url, headers=headers, timeout=10)

        if response.status_code != 200:
            return Response(
                json.dumps({
                    "error": f"Unsplash API returned status {response.status_code}",
                    "details": response.text
                }),
                status=response.status_code,
                headers={"Content-Type": "application/json"}
            )

        data = response.json()
        results = data.get("results", [])

        if not results:
            return Response(
                json.dumps({"error": "No images found for the given query"}),
                status=404,
                headers={"Content-Type": "application/json"}
            )

        image_url = results[0].get("urls", {}).get("regular")

        if not image_url:
            return Response(
                json.dumps({"error": "Image URL not found in API response"}),
                status=404,
                headers={"Content-Type": "application/json"}
            )

        return Response(
            json.dumps({"imageUrl": image_url}),
            status=200,
            headers={"Content-Type": "application/json"}
        )

    except requests.exceptions.Timeout:
        return Response(
            json.dumps({"error": "Request to Unsplash API timed out"}),
            status=408,
            headers={"Content-Type": "application/json"}
        )
    except requests.exceptions.RequestException as e:
        return Response(
            json.dumps({"error": f"Network error: {str(e)}"}),
            status=503,
            headers={"Content-Type": "application/json"}
        )
    except Exception as e:
        return Response(
            json.dumps({"error": f"Internal server error: {str(e)}"}),
            status=500,
            headers={"Content-Type": "application/json"}
        )


def get_existing_recipe_ids(uid: str) -> list:
    """
    Retrieve existing recipe IDs for a user from Firestore subcollection.

    Args:
        uid: User's unique identifier

    Returns:
        list: List of existing recipe IDs, empty list if no recipes found
    """
    db = get_db()
    recipes_ref = db.collection('generatedRecipes').document(uid).collection('recipes')

    # Get only the document IDs (recipe IDs) without fetching full documents
    docs = recipes_ref.select([]).get()

    return [doc.id for doc in docs]


def get_user_diet_preferences(uid: str) -> dict:
    """
    Retrieve user's diet preferences from Firestore.

    Args:
        uid: User's unique identifier

    Returns:
        dict: User's diet preferences

    Raises:
        ValueError: If diet preferences not found
    """
    db = get_db()
    diet_prefs_ref = db.collection('dietPreferences').document(uid)
    diet_prefs_doc = diet_prefs_ref.get()

    if not diet_prefs_doc.exists:
        raise ValueError("Diet preferences not found. Please complete onboarding first.")

    return diet_prefs_doc.to_dict()


def generate_recipes_openai(diet_preferences: dict, uid: str, numRecipes: int = 3, existing_recipe_ids: list = None) -> list:
    """
    Generate recipes using OpenAI API based on user's diet preferences.

    Args:
        diet_preferences: User's dietary preferences and restrictions
        uid: User's unique identifier for API tracking
        numRecipes: Number of recipes to generate (default: 3)
        existing_recipe_ids: List of existing recipe IDs to avoid duplicating (optional)

    Returns:
        list: Generated recipes

    Raises:
        ValueError: If OpenAI API key not found or API call fails
        json.JSONDecodeError: If response parsing fails
    """
    # Get OpenAI API key
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        raise ValueError("OpenAI API key not found")

    client = OpenAI(api_key=api_key)

    # Define the recipe generation schema
    # !!!! Keep this in sync with the TypeScript schema in app/schemas/recipeGeneration.ts !!!!
    recipe_generation_schema = {
        "type": "object",
        "properties": {
            "recipes": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "string"},
                        "compatibleDiets": {
                            "type": "array",
                            "items": {
                                "type": "string",
                                "additionalProperties": False,
                            },
                        },
                        "title": {"type": "string"},
                        "timeInMinutes": {"type": "number"},
                        "calories": {"type": "number"},
                        "ingredients": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string"},
                                    "amount": {"type": "string"},
                                    "unit": {"type": "string"},
                                    "available": {"type": "boolean"}
                                },
                                "required": ["name", "amount", "unit", "available"],
                                "additionalProperties": False
                            }
                        },
                        "instructions": {
                            "type": "object",
                            "properties": {
                                "High level": {"type": "string"},
                                "Detailed": {"type": "string"},
                                "Teach mode": {"type": "string"}
                            },
                            "required": ["High level", "Detailed", "Teach mode"],
                            "additionalProperties": False
                        },
                        "imageQuery": {"type": "string"},
                        "mealType": {
                            "type": "string",
                            "enum": ["Breakfast", "Lunch", "Dinner", "Dessert"]  
                        }
                    },
                    "required": ["id", "compatibleDiets", "title", "timeInMinutes", "calories", "ingredients", "instructions", "imageQuery", "mealType"],
                    "additionalProperties": False
                }
            }
        },
        "required": ["recipes"],
        "additionalProperties": False
    }

    # Create instructions for recipe generation
    existing_recipes_instruction = ""
    if existing_recipe_ids and len(existing_recipe_ids) > 0:
        existing_recipes_instruction = f"""
      IMPORTANT:
      - The user already has these recipe IDs in their collection: {existing_recipe_ids}.
      - DO NOT generate recipes that are duplicates or highly similar to these existing ones.
      - Ensure variety in cuisines and core ingredients across the generated recipes.
      """

    instructions = f"""
      You are a professional chef and nutritionist helping the user discover new, practical, and healthy recipes.

      User's dietary preferences:
      {json.dumps(diet_preferences)}

      Your goals:
      - Strictly follow the user's dietary preferences, restrictions, and lifestyle.
      - Generate diverse, nutritious, realistic recipes that fit the user's cooking experience.
      - Ensure accuracy in calorie estimates and ingredient measurements.

      {existing_recipes_instruction}

      TASK:
      Generate exactly {numRecipes} new recipes with the following distribution:
      - {numRecipes // 4} Breakfast
      - {numRecipes // 4} Lunch
      - {numRecipes // 4} Dinner
      - {numRecipes // 4} Dessert

      For each recipe, output ALL of the following fields:
      1. **dietHashSlugId**: <8-char SHA-256 hash>_<slugified-title>
        - Hash is computed over the canonical JSON of title + ingredients.
        - Example: `3fa9c2d7_chickpea-curry`
      2. **Title**: A descriptive, appetizing recipe title.
      3. **Preparation time**: Total minutes from start to finish (realistic).
      4. **Calories per serving**: Accurately based on specified ingredient amounts.
      5. **Ingredients**:
        - Precise serving size (amount + unit, e.g., "200 grams", "2 tablespoons").
        - Availability field (set all to `false` for now).
      6. **Instructions** (three versions):
        - **High Level**: Concise overview for experienced cooks.
        - **Detailed**: Step-by-step with clear explanations.
        - **Teach Mode**: Beginner-friendly with detailed cooking science, technique tips, and reasoning.
      7. **Image Search Query**: Exactly two words that clearly identify the dish as food (e.g., `mango curry`).
      8. **Meal Type**: Breakfast, Lunch, Dinner, or Dessert.

      RECIPE QUALITY RULES:
      - No duplicates or close variants of existing recipes.  
      - Use a wide range of cuisines, cooking methods, and key ingredients.  
      - Recipes must be realistic for a home cook, with accessible ingredients and steps.  
      - Balance health, flavor, and practicality.

      Final Output:  
      - Return results in valid JSON strictly following `recipe_generation_schema`. 
      """

    # Create the prompt
    prompt = f"""
      Generate {numRecipes} diverse, practical, and delicious recipes tailored to my dietary preferences.
      Include exactly {numRecipes // 4} recipes each for breakfast, lunch, dinner, and dessert.
      Every recipe must include precise ingredient amounts, realistic prep times, accurate calories, and all required fields.
      """

    # Prepare the API call parameters
    params = {
        "model": "gpt-5-mini",
        "input": [{"role": "developer", "content": prompt}],
        "instructions": instructions,
        "text": {
            "format": {
                "name": "recipes",
                "schema": recipe_generation_schema,
                "type": "json_schema"
            }
        },
        "user": uid
    }

    response = client.responses.create(**params)
    output_content = response.output_text

    if not output_content:
        raise ValueError("No response content from OpenAI API")

    # Parse the JSON response
    try:
        parsed_response = json.loads(output_content)
    except json.JSONDecodeError as e:
        raise json.JSONDecodeError(f"Failed to parse OpenAI response: {str(e)}", output_content, 0)

    if not parsed_response.get("recipes") or not isinstance(parsed_response["recipes"], list):
        raise ValueError("Invalid response format from OpenAI")

    recipes = parsed_response["recipes"]

    return recipes


def store_recipes(uid: str, recipes: list) -> None:
    """
    Store generated recipes in Firestore as individual documents in a subcollection.
    Each recipe is stored as a separate document in generatedRecipes/{uid}/recipes/{recipeId}

    Args:
        uid: User's unique identifier
        recipes: List of generated recipes

    Raises:
        Exception: If Firestore operation fails
    """
    db = get_db()

    # Get references to the user document and recipes subcollection
    user_doc_ref = db.collection('generatedRecipes').document(uid)
    recipes_collection_ref = user_doc_ref.collection('recipes')

    # Use a batch write for better performance and atomicity
    batch = db.batch()

    # Store each recipe as a separate document
    for recipe in recipes:
        recipe_id = recipe.get("id")
        if not recipe_id:
            continue

        # Add generatedAt timestamp to each recipe
        recipe_data = {
            **recipe,
            "generatedAt": firestore.SERVER_TIMESTAMP,
        }

        # Add recipe document to batch
        recipe_doc_ref = recipes_collection_ref.document(recipe_id)
        batch.set(recipe_doc_ref, recipe_data)

    # Update user document with metadata
    user_metadata = {
        "lastUpdated": firestore.SERVER_TIMESTAMP,
        "totalRecipes": firestore.Increment(len(recipes))
    }

    # Check if user document exists, if not create it
    if not user_doc_ref.get().exists:
        user_metadata["createdAt"] = firestore.SERVER_TIMESTAMP
        user_metadata["totalRecipes"] = len(recipes)  # Don't use Increment for new documents

    batch.set(user_doc_ref, user_metadata, merge=True)

    # Commit the batch
    batch.commit()

    print(f"Successfully stored {len(recipes)} recipes for user {uid}")


@firestore_fn.on_document_created(document="dietPreferences/{uid}", secrets=["OPENAI_API_KEY"], timeout_sec=500)
def generate_recipes_on_diet_preferences_created(event: firestore_fn.Event[firestore_fn.DocumentSnapshot]) -> None:
    """
    Generate 4 recipes (1 breakfast, 1 lunch, 1 dinner, 1 dessert) when a new dietPreferences document is created.
    This function is triggered automatically when a user completes onboarding and their diet preferences are saved.
    """
    try:
        # Get the UID from the document path
        uid = event.params["uid"]

        # Get the diet preferences from the created document
        diet_preferences = event.data.to_dict()

        if not diet_preferences:
            print(f"No diet preferences found for user {uid}")
            return

        print(f"Generating recipes for user {uid} with preferences: {diet_preferences}")

        # Step 1: Generate recipes using OpenAI
        recipes = generate_recipes_openai(diet_preferences, uid, 4, [])

        # Step 2: Store recipes in Firestore
        store_recipes(uid, recipes)

        print(f"Successfully generated and stored {len(recipes)} recipes for user {uid}")

    except Exception as e:
        print(f"Error generating recipes for user {event.params.get('uid', 'unknown')}: {str(e)}")
        # Don't raise the exception to avoid retries for this background function


@https_fn.on_request(timeout_sec=60)
def get_or_create_dm(req: Request) -> Response:
    """
    HTTP Cloud Function to get or create a DM conversation.

    Body: { "uid": "...", "type": "bot_dm", "botId": "coach" }
    Returns: { "conversationId": "..." }

    Ensures a single DM exists for dmKey using a transaction.
    Creates /conversations/{id}, participants, and the key guard /conversationKeys/{dmKey}.
    """
    ensure_app()

    if req.method != "POST":
        return Response(
            json.dumps({"error": "POST only"}),
            status=405,
            headers={"Content-Type": "application/json"}
        )

    try:
        # Verify authentication
        id_token = req.headers.get("Authorization", "").replace("Bearer ", "")
        if not id_token:
            return Response(
                json.dumps({"error": "Authorization header required"}),
                status=401,
                headers={"Content-Type": "application/json"}
            )

        decoded_token = admin_auth.verify_id_token(id_token)
        authenticated_uid = decoded_token["uid"]

        # Parse request body
        body = req.get_json(silent=True) or {}
        uid = body.get("uid")
        dm_type = body.get("type", "bot_dm")
        bot_id = body.get("botId", "coach")

        if not uid:
            return Response(
                json.dumps({"error": "uid required"}),
                status=400,
                headers={"Content-Type": "application/json"}
            )

        # Verify the authenticated user matches the requested uid
        if authenticated_uid != uid:
            return Response(
                json.dumps({"error": "Unauthorized: uid mismatch"}),
                status=403,
                headers={"Content-Type": "application/json"}
            )

        # Build deterministic dmKey
        if dm_type == "bot_dm":
            dm_key = f"bot:{bot_id}|user:{uid}"
        else:
            return Response(
                json.dumps({"error": "unsupported type for this endpoint"}),
                status=400,
                headers={"Content-Type": "application/json"}
            )

        db = get_db()
        key_ref = db.collection("conversationKeys").document(dm_key)

        @firestore.transactional
        def run_transaction(transaction):
            key_snap = key_ref.get(transaction=transaction)
            if key_snap.exists:
                convo_id = key_snap.get("conversationId")
                return convo_id

            # Create new conversation
            convo_ref = db.collection("conversations").document()
            transaction.set(convo_ref, {
                "type": dm_type,
                "dmKey": dm_key,
                "createdAt": firestore.SERVER_TIMESTAMP,
                "createdBy": bot_id,
                # header fields (lastMessage, lastMessageAt, lastSenderId)
                # are populated on first message by the message trigger
            })

            # Create participants
            transaction.set(convo_ref.collection("participants").document(uid), {
                "role": "user",
                "lastReadAt": firestore.SERVER_TIMESTAMP,
                "unreadCount": 0,
                "muted": False,
                "notifications": "all",
            })
            transaction.set(convo_ref.collection("participants").document(bot_id), {
                "role": "bot",
                "unreadCount": 0,
                "muted": False,
                "notifications": "none",
            })

            # Write the uniqueness guard LAST
            transaction.set(key_ref, {
                "conversationId": convo_ref.id,
                "type": dm_type,
                "createdAt": firestore.SERVER_TIMESTAMP,
            })
            return convo_ref.id

        # Execute transaction
        transaction = db.transaction()
        conversation_id = run_transaction(transaction)

        print(f"Get or create DM successful for uid={uid}, conversationId={conversation_id}")

        return Response(
            json.dumps({"conversationId": conversation_id}),
            status=200,
            headers={"Content-Type": "application/json"}
        )

    except admin_auth.InvalidIdTokenError:
        return Response(
            json.dumps({"error": "Invalid authentication token"}),
            status=401,
            headers={"Content-Type": "application/json"}
        )
    except Exception as e:
        print(f"Error in get_or_create_dm: {str(e)}")
        return Response(
            json.dumps({"error": f"Internal server error: {str(e)}"}),
            status=500,
            headers={"Content-Type": "application/json"}
        )


@firestore_fn.on_document_created(document="conversations/{cid}/messages/{mid}", timeout_sec=60)
def on_message_created(event: firestore_fn.Event[firestore_fn.DocumentSnapshot]) -> None:
    """
    Firestore trigger when a new message is created.
    Updates conversation header, increments unread counts, and mirrors inbox.
    """
    try:
        # Get conversation and message IDs from the event path
        cid = event.params["cid"]
        mid = event.params["mid"]

        # Get message data
        message_data = event.data.to_dict()
        if not message_data:
            print(f"No message data found for message {mid} in conversation {cid}")
            return

        text = message_data.get("text", "")
        sender_id = message_data.get("senderId", "")
        created_at = message_data.get("createdAt")

        if not sender_id:
            print(f"No senderId found for message {mid}")
            return

        db = get_db()
        convo_ref = db.collection("conversations").document(cid)
        parts_ref = convo_ref.collection("participants")

        # Transaction to update header + unread + inbox mirrors
        @firestore.transactional
        def run_transaction(transaction):
            # Update conversation header
            transaction.update(convo_ref, {
                "lastMessage": text[:140] if text else "",
                "lastMessageAt": created_at or firestore.SERVER_TIMESTAMP,
                "lastSenderId": sender_id,
            })

            # Get all participants
            parts_docs = list(transaction.get_query(parts_ref.stream()))
            recipient_uids = []

            for part_doc in parts_docs:
                uid = part_doc.id
                pdata = part_doc.to_dict() or {}

                # Skip the sender
                if uid == sender_id:
                    continue

                # Skip if notifications are disabled (but still increment unread)
                if pdata.get("notifications") == "none":
                    pass

                # Increment unread count
                part_doc_ref = parts_ref.document(uid)
                unread = int(pdata.get("unreadCount", 0)) + 1
                transaction.update(part_doc_ref, {"unreadCount": unread})

                # Mirror to inbox
                inbox_ref = db.document(f"users/{uid}/inbox/{cid}")
                transaction.set(inbox_ref, {
                    "conversationId": cid,
                    "lastMessage": text[:140] if text else "",
                    "lastMessageAt": created_at or firestore.SERVER_TIMESTAMP,
                    "lastSenderId": sender_id,
                    "unreadCount": unread,
                }, merge=True)

                recipient_uids.append(uid)

            return recipient_uids

        # Execute transaction
        transaction = db.transaction()
        recipient_uids = run_transaction(transaction)

        print(f"Message {mid} processed in conversation {cid}. Recipients: {recipient_uids}")

        # Send push notifications to recipients
        if recipient_uids:
            send_push_notifications(db, cid, mid, text, sender_id, recipient_uids)

    except Exception as e:
        print(f"Error in on_message_created: {str(e)}")
        # Don't raise to avoid retries


def send_push_notifications(db, cid: str, mid: str, text: str, sender_id: str, recipient_uids: list):
    """
    Send push notifications to recipients of a message.
    """
    try:
        # Get conversation participants to check notification settings
        parts_ref = db.collection("conversations").document(cid).collection("participants")

        for uid in recipient_uids:
            try:
                # Get participant data to check if muted
                part_doc = parts_ref.document(uid).get()
                if not part_doc.exists:
                    continue

                pdata = part_doc.to_dict() or {}

                # Skip if notifications are disabled
                if pdata.get("notifications") == "none":
                    print(f"Skipping push notification for user {uid}: notifications disabled")
                    continue

                # Skip if muted
                if pdata.get("muted"):
                    print(f"Skipping push notification for user {uid}: conversation muted")
                    continue

                # Get user's push tokens
                tokens_ref = db.collection(f"users/{uid}/pushTokens")
                tokens_snapshot = tokens_ref.stream()
                tokens = []

                for token_doc in tokens_snapshot:
                    token_data = token_doc.to_dict()
                    if token_data and token_data.get("token"):
                        tokens.append(token_data["token"])

                if not tokens:
                    print(f"No push tokens found for user {uid}")
                    continue

                # Determine notification title and body
                if sender_id == "coach":
                    title = "ChefPal Coach"
                    body = text if text else "New message from your coach"
                else:
                    title = "ChefPal"
                    body = text if text else "New message"

                # Create notification
                notification = messaging.Notification(
                    title=title,
                    body=body[:200] if body else "New message",  # Limit body length
                )

                # iOS-specific configuration
                apns = messaging.APNSConfig(
                    headers={
                        "apns-collapse-id": cid,  # Group notifications by conversation
                        "apns-thread-id": cid,     # Thread notifications together
                    },
                    payload=messaging.APNSPayload(
                        aps=messaging.Aps(
                            sound="default",
                            badge=pdata.get("unreadCount", 1),  # Set badge to unread count
                            content_available=False,
                            mutable_content=False,
                        )
                    ),
                )

                # Android-specific configuration
                android = messaging.AndroidConfig(
                    collapse_key=cid,  # Group notifications by conversation
                    priority="high",
                    notification=messaging.AndroidNotification(
                        sound="default",
                        channel_id="coaching_messages",  # Custom channel for coaching messages
                    ),
                )

                # Data payload for deep linking
                data = {
                    "conversationId": cid,
                    "messageId": mid,
                    "senderId": sender_id,
                    "type": "coaching_message",
                }

                # Send multicast message to all user's devices
                message = messaging.MulticastMessage(
                    tokens=tokens,
                    notification=notification,
                    android=android,
                    apns=apns,
                    data=data,
                )

                response = messaging.send_multicast(message)
                print(f"Push notification sent to user {uid}: {response.success_count} successful, {response.failure_count} failed")

                # Clean up invalid tokens
                if response.failure_count > 0:
                    for idx, result in enumerate(response.responses):
                        if not result.success:
                            # Remove invalid token
                            invalid_token = tokens[idx]
                            print(f"Removing invalid token for user {uid}: {result.exception}")
                            # Find and delete the token document
                            token_docs = tokens_ref.where("token", "==", invalid_token).stream()
                            for token_doc in token_docs:
                                token_doc.reference.delete()

            except Exception as user_error:
                print(f"Error sending push notification to user {uid}: {str(user_error)}")
                continue

    except Exception as e:
        print(f"Error in send_push_notifications: {str(e)}")


@scheduler_fn.on_schedule(schedule="0 0 * * *", timezone="UTC", secrets=["OPENAI_API_KEY"], timeout_sec=600)
def generate_recipes_daily(_event: scheduler_fn.ScheduledEvent) -> None:
    """
    Scheduled function that runs every 24 hours at midnight UTC to generate fresh recipes
    for weekly active users who have diet preferences stored in Firestore.

    This function:
    1. Queries users collection for users active within the last 7 days (based on lastActiveAt field)
    2. For each active user, checks if they have diet preferences
    3. Generates 3 new recipes (1 breakfast, 1 lunch, 1 dinner) for active users with diet preferences
    4. Stores the recipes in Firestore, appending to existing recipes

    Schedule: "0 0 * * *" means:
    - 0 minutes past the hour
    - 0 hours (midnight)
    - Every day of the month
    - Every month
    - Every day of the week
    """
    print("Starting daily recipe generation for weekly active users...")

    try:
        from datetime import datetime, timedelta

        # Calculate the date 7 days ago
        seven_days_ago = datetime.now() - timedelta(days=7)
        print(f"Looking for users active since: {seven_days_ago}")

        # Get weekly active users from users collection
        db = get_db()
        users_collection = db.collection('users')

        # Query users who have been active within the last 7 days
        active_users_query = users_collection.where('lastActiveAt', '>=', seven_days_ago)
        active_users_docs = active_users_query.stream()

        users_processed = 0
        users_failed = 0
        users_skipped = 0
        total_recipes_generated = 0

        for user_doc in active_users_docs:
            uid = user_doc.id
            user_data = user_doc.to_dict()
            last_active = user_data.get('lastActiveAt')

            print(f"Processing active user {uid} (last active: {last_active})")

            try:
                # Check if user has diet preferences
                diet_prefs_ref = db.collection('dietPreferences').document(uid)
                diet_prefs_doc = diet_prefs_ref.get()

                if not diet_prefs_doc.exists:
                    print(f"User {uid} is active but has no diet preferences, skipping")
                    users_skipped += 1
                    continue

                diet_preferences = diet_prefs_doc.to_dict()
                print(f"Generating recipes for active user {uid}")

                # Get existing recipe IDs to avoid duplicates
                existing_recipe_ids = get_existing_recipe_ids(uid)
                print(f"User {uid} has {len(existing_recipe_ids)} existing recipes")

                # Generate recipes using OpenAI, passing existing recipe IDs to avoid duplicates
                recipes = generate_recipes_openai(diet_preferences, uid, 3, existing_recipe_ids)

                # Store recipes in Firestore
                store_recipes(uid, recipes)

                users_processed += 1
                total_recipes_generated += len(recipes)
                print(f"Successfully generated {len(recipes)} recipes for user {uid}")

            except Exception as user_error:
                users_failed += 1
                print(f"Failed to generate recipes for user {uid}: {str(user_error)}")
                # Continue processing other users even if one fails
                continue

        print(f"Daily recipe generation completed. Active users processed: {users_processed}, Failed: {users_failed}, Skipped (no diet prefs): {users_skipped}, Total recipes generated: {total_recipes_generated}")

    except Exception as e:
        print(f"Error in daily recipe generation: {str(e)}")


@scheduler_fn.on_schedule(schedule="0 * * * *", timezone="UTC", secrets=["OPENAI_API_KEY"], timeout_sec=600)
def send_daily_coaching_messages(_event: scheduler_fn.ScheduledEvent) -> None:
    """
    Scheduled function that runs every hour to send coaching messages to users at 3 PM in their local timezone.

    This function:
    1. Runs every hour (on the hour)
    2. For each user with diet preferences, checks their timezone (stored in users collection)
    3. Determines if it's currently 3 PM (15:00) in their timezone
    4. If so, checks if a message was already sent today
    5. Generates and sends a personalized coaching tip using OpenAI

    Schedule: "0 * * * *" means:
    - 0 minutes past the hour
    - Every hour
    - Every day of the month
    - Every month
    - Every day of the week
    """
    try:
        from datetime import datetime, timedelta
        import pytz

        db = get_db()
        current_utc_time = datetime.now(pytz.UTC)

        print(f"Starting hourly coaching message check at {current_utc_time}")

        # Get all users with diet preferences
        diet_prefs_collection = db.collection("dietPreferences")
        diet_prefs_docs = diet_prefs_collection.stream()

        users_processed = 0
        users_failed = 0
        users_skipped = 0
        total_messages_sent = 0

        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            print("OpenAI API key not found, cannot send coaching messages")
            return

        client = OpenAI(api_key=api_key)

        for diet_pref_doc in diet_prefs_docs:
            try:
                uid = diet_pref_doc.id
                diet_preferences = diet_pref_doc.to_dict()

                if not diet_preferences:
                    print(f"No diet preferences found for user {uid}")
                    continue

                # Get user's timezone from users collection
                user_doc_ref = db.collection("users").document(uid)
                user_doc = user_doc_ref.get()

                if not user_doc.exists:
                    print(f"User document not found for {uid}, skipping")
                    users_skipped += 1
                    continue

                user_data = user_doc.to_dict()
                user_timezone_str = user_data.get("timezone")

                if not user_timezone_str:
                    print(f"No timezone set for user {uid}, skipping")
                    users_skipped += 1
                    continue

                # Convert user's timezone string to timezone object
                try:
                    user_timezone = pytz.timezone(user_timezone_str)
                except pytz.exceptions.UnknownTimeZoneError:
                    print(f"Invalid timezone '{user_timezone_str}' for user {uid}, skipping")
                    users_skipped += 1
                    continue

                # Get current time in user's timezone
                user_local_time = current_utc_time.astimezone(user_timezone)
                user_hour = user_local_time.hour

                # Check if it's 5 PM (17:00) in user's timezone
                if user_hour != 17:
                    # Not 5 PM for this user, skip
                    continue

                # Check if we already sent a message today
                last_coaching_message_date = user_data.get("lastCoachingMessageDate")
                today_date_str = user_local_time.strftime("%Y-%m-%d")

                if last_coaching_message_date and isinstance(last_coaching_message_date, str):
                    if last_coaching_message_date == today_date_str:
                        print(f"Already sent coaching message to user {uid} today ({today_date_str}), skipping")
                        users_skipped += 1
                        continue

                print(f"Generating coaching message for user {uid} (local time: {user_local_time.strftime('%Y-%m-%d %H:%M %Z')})")

                # Get or create conversation using dmKey with transaction
                dm_key = f"bot:coach|user:{uid}"
                key_ref = db.collection("conversationKeys").document(dm_key)

                @firestore.transactional
                def get_or_create_conversation(transaction):
                    key_snap = key_ref.get(transaction=transaction)
                    if key_snap.exists:
                        convo_id = key_snap.get("conversationId")
                        return convo_id

                    # Create new conversation with unique ID
                    convo_ref = db.collection("conversations").document()
                    transaction.set(convo_ref, {
                        "type": "bot_dm",
                        "dmKey": dm_key,
                        "createdAt": firestore.SERVER_TIMESTAMP,
                        "createdBy": "coach",
                        # header fields (lastMessage, lastMessageAt, lastSenderId)
                        # are populated on first message by the message trigger
                    })

                    # Create participants
                    transaction.set(convo_ref.collection("participants").document(uid), {
                        "role": "user",
                        "lastReadAt": firestore.SERVER_TIMESTAMP,
                        "unreadCount": 0,
                        "muted": False,
                        "notifications": "all",
                    })
                    transaction.set(convo_ref.collection("participants").document("coach"), {
                        "role": "bot",
                        "unreadCount": 0,
                        "muted": False,
                        "notifications": "none",
                    })

                    # Write the uniqueness guard LAST
                    transaction.set(key_ref, {
                        "conversationId": convo_ref.id,
                        "type": "bot_dm",
                        "createdAt": firestore.SERVER_TIMESTAMP,
                    })
                    return convo_ref.id

                # Execute transaction
                transaction = db.transaction()
                cid = get_or_create_conversation(transaction)
                print(f"Got or created coach conversation {cid} for user {uid}")

                # Fetch recent coaching messages (last 14 days) for context
                fourteen_days_ago = datetime.now() - timedelta(days=14)
                messages_ref = db.collection(f"conversations/{cid}/messages")
                recent_messages_query = messages_ref.where("senderId", "==", "coach").where("createdAt", ">=", fourteen_days_ago).order_by("createdAt", "desc").limit(10)
                recent_messages_docs = recent_messages_query.stream()

                recent_coaching_tips = []
                for msg_doc in recent_messages_docs:
                    msg_data = msg_doc.to_dict()
                    if msg_data and msg_data.get("text"):
                        recent_coaching_tips.append(msg_data["text"])

                # Build context for LLM
                context_section = ""
                if recent_coaching_tips:
                    context_section = f"""
                IMPORTANT - PREVIOUS COACHING TIPS (Last 14 days):
                The following tips have already been sent to this user. DO NOT repeat these exact tips or very similar advice:
                {chr(10).join([f"- {tip}" for tip in recent_coaching_tips])}

                Generate a NEW and DIFFERENT tip that:
                - Covers a different aspect of nutrition, meal planning, or healthy eating
                - Uses different phrasing and approach
                - Provides fresh, actionable advice
                - Note: It's acceptable to remind about recurring habits (hydration, vitamins, meal prep) but vary the approach and phrasing significantly
                """

                # Generate personalized coaching tip using OpenAI
                prompt = f"""
                Generate a brief, friendly, and actionable daily coaching tip for a user with the following dietary preferences:

                Diet Type: {diet_preferences.get('diet', 'Not specified')}
                Allergies: {', '.join(diet_preferences.get('allergies', [])) if diet_preferences.get('allergies') else 'None'}
                Cooking Experience: {diet_preferences.get('experience', 'Not specified')}
                Time to Cook: {diet_preferences.get('timeToCook', 'Not specified')}
                Calorie Goal: {diet_preferences.get('calories', 'Not specified')}
                Goals: {diet_preferences.get('goals', 'Not specified')}

                {context_section}

                The tip should be:
                - Personalized to their dietary preferences and goals
                - Practical and easy to implement
                - Encouraging and positive
                - 2-3 sentences maximum
                - Related to nutrition, meal planning, or healthy eating habits
                - DIFFERENT from the previous tips listed above

                Start with a friendly greeting like "Good morning!" or "Hey there!" and end with an encouraging note.
                """

                try:
                    response = client.chat.completions.create(
                        model="gpt-5-mini",
                        messages=[
                            {"role": "system", "content": "You are a friendly and knowledgeable nutrition coach who provides personalized, actionable advice. You keep track of what you've already shared and always provide fresh, varied tips."},
                            {"role": "user", "content": prompt}
                        ],
                        max_tokens=150,
                        temperature=0.8,
                    )

                    coaching_message = response.choices[0].message.content.strip()

                except Exception as openai_error:
                    print(f"Error generating coaching message with OpenAI for user {uid}: {str(openai_error)}")
                    # Fallback to a generic message
                    coaching_message = "Good morning! 🌅 Remember, small consistent changes lead to big results. Today, try to include one extra serving of vegetables in your meals. You've got this! 💪"

                # Send the message
                msg_ref = db.collection(f"conversations/{cid}/messages").document()
                msg_ref.set({
                    "text": coaching_message,
                    "senderId": "coach",
                    "createdAt": firestore.SERVER_TIMESTAMP,
                    "type": "bot",
                    "dedupeKey": msg_ref.id,
                })

                # Update user document with lastCoachingMessageDate to prevent duplicate messages
                user_doc_ref.update({
                    "lastCoachingMessageDate": today_date_str
                })

                users_processed += 1
                total_messages_sent += 1
                print(f"Successfully sent coaching message to user {uid} in conversation {cid}")

            except Exception as user_error:
                users_failed += 1
                print(f"Failed to send coaching message to user {uid}: {str(user_error)}")
                continue

        print(f"Hourly coaching message check completed. Messages sent: {total_messages_sent}, Users processed: {users_processed}, Failed: {users_failed}, Skipped: {users_skipped}")

    except Exception as e:
        print(f"Error in send_daily_coaching_messages: {str(e)}")