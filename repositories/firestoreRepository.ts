import {
  db,
  dietPreferenceCollection,
  conversationCollection,
  inventoryCollection,
  groceryListCollection,
  generatedRecipesCollection,
  usersCollection,
  savedRecipesCollection,
} from '../firebase/firebaseConfig';
import {
  FirebaseFirestoreTypes,
  getDocs,
  getDoc,
  addDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  doc,
  collection,
  query,
  orderBy,
  limit,
  startAfter,
} from '@react-native-firebase/firestore';
import {
  COLLECTIONS,
  SUBCOLLECTIONS,
  CollectionPaths,
  dmKeyForBot,
  dmKeyForUsers,
} from '../constants/FirestoreCollections';

// Type aliases for react-native-firebase
type DocumentData = FirebaseFirestoreTypes.DocumentData;
type QueryDocumentSnapshot = FirebaseFirestoreTypes.QueryDocumentSnapshot<DocumentData>;

// Re-export for convenience
export { COLLECTIONS, SUBCOLLECTIONS, CollectionPaths, dmKeyForBot, dmKeyForUsers };

// Helper function to get the collection reference
const getCollectionRef = (collectionName: string) => {
  switch (collectionName) {
    case COLLECTIONS.DIET_PREFERENCES:
      return dietPreferenceCollection;
    case COLLECTIONS.CONVERSATIONS:
      return conversationCollection;
    case COLLECTIONS.INVENTORY:
      return inventoryCollection;
    case COLLECTIONS.GROCERY_LIST:
      return groceryListCollection;
    case COLLECTIONS.GENERATED_RECIPES:
      return generatedRecipesCollection;
    case COLLECTIONS.SAVED_RECIPES:
      return savedRecipesCollection;
    case COLLECTIONS.USERS:
      return usersCollection;
    default:
      throw new Error(`Unknown collection: ${collectionName}`);
  }
};

export interface PaginationResult<T> {
  data: T[];
  lastVisible: QueryDocumentSnapshot | null;
  hasMore: boolean;
}

export interface FirestoreRepository {
  getCollection: (collectionName: string) => Promise<DocumentData[]>;
  getDocument: (collectionName: string, docId: string) => Promise<DocumentData | null>;
  addDocument: (collectionName: string, data: DocumentData) => Promise<string>;
  updateDocument: (collectionName: string, docId: string, data: DocumentData) => Promise<void>;
  deleteDocument: (collectionName: string, docId: string) => Promise<void>;
  addOrReplaceDocument: (collectionName: string, docId: string, data: object) => Promise<void>;
  getSubcollection: (
    parentCollection: string,
    parentDocId: string,
    subcollectionName: string
  ) => Promise<DocumentData[]>;
  addSubcollectionDocument: (
    parentCollection: string,
    parentDocId: string,
    subcollectionName: string,
    docId: string,
    data: DocumentData
  ) => Promise<void>;
  addSubcollectionDocumentWithAutoId: (
    parentCollection: string,
    parentDocId: string,
    subcollectionName: string,
    data: DocumentData
  ) => Promise<string>;
  deleteSubcollectionDocument: (
    parentCollection: string,
    parentDocId: string,
    subcollectionName: string,
    docId: string
  ) => Promise<void>;
  getPaginatedRecipes: (
    userId: string,
    pageSize: number,
    lastVisible?: QueryDocumentSnapshot | null
  ) => Promise<PaginationResult<any>>;
}

const getCollection = async (collectionName: string): Promise<DocumentData[]> => {
  const collectionRef = getCollectionRef(collectionName);
  const querySnapshot = await getDocs(collectionRef);
  return querySnapshot.docs.map((doc: QueryDocumentSnapshot) => ({ id: doc.id, ...doc.data() }));
};

const addDocument = async (collectionName: string, data: DocumentData): Promise<string> => {
  const collectionRef = getCollectionRef(collectionName);
  const docRef = await addDoc(collectionRef, data);
  return docRef.id;
};

const getDocument = async (collectionName: string, docId: string): Promise<DocumentData | null> => {
  const docRef = doc(db, collectionName, docId);
  const docSnap = await getDoc(docRef);
  if (docSnap.exists()) {
    return { id: docSnap.id, ...docSnap.data() };
  }
  return null;
};

// Function that adds or replaces a document in a specified collection.
// If the document doesn't exist, it creates it. If it does, it overwrites it.
const addOrReplaceDocument = async (collectionName: string, docId: string, data: object): Promise<void> => {
  try {
    // Using set without merge will replace the existing document if it exists.
    const docRef = doc(db, collectionName, docId);
    await setDoc(docRef, data);
    console.log(`Document "${docId}" in collection "${collectionName}" has been successfully added/replaced.`);
  } catch (error) {
    console.error('Error adding/replacing document:', error);
    throw error;
  }
};

const updateDocument = async (collectionName: string, docId: string, data: DocumentData): Promise<void> => {
  const docRef = doc(db, collectionName, docId);
  await updateDoc(docRef, data);
};

const deleteDocument = async (collectionName: string, docId: string): Promise<void> => {
  const docRef = doc(db, collectionName, docId);
  await deleteDoc(docRef);
};

// Subcollection methods
const getSubcollection = async (
  parentCollection: string,
  parentDocId: string,
  subcollectionName: string
): Promise<DocumentData[]> => {
  const subcollectionRef = collection(db, parentCollection, parentDocId, subcollectionName);
  const querySnapshot = await getDocs(subcollectionRef);
  return querySnapshot.docs.map((doc: QueryDocumentSnapshot) => ({ id: doc.id, ...doc.data() }));
};

const addSubcollectionDocument = async (
  parentCollection: string,
  parentDocId: string,
  subcollectionName: string,
  docId: string,
  data: DocumentData
): Promise<void> => {
  const docRef = doc(db, parentCollection, parentDocId, subcollectionName, docId);
  await setDoc(docRef, data);
};

const addSubcollectionDocumentWithAutoId = async (
  parentCollection: string,
  parentDocId: string,
  subcollectionName: string,
  data: DocumentData
): Promise<string> => {
  const subcollectionRef = collection(db, parentCollection, parentDocId, subcollectionName);
  const docRef = await addDoc(subcollectionRef, data);
  return docRef.id;
};

const deleteSubcollectionDocument = async (
  parentCollection: string,
  parentDocId: string,
  subcollectionName: string,
  docId: string
): Promise<void> => {
  const docRef = doc(db, parentCollection, parentDocId, subcollectionName, docId);
  await deleteDoc(docRef);
};

const getPaginatedRecipes = async (
  userId: string,
  pageSize: number,
  lastVisible?: QueryDocumentSnapshot | null
): Promise<PaginationResult<any>> => {
  try {
    // Query the recipes subcollection with proper Firestore pagination
    const recipesCollectionRef = collection(
      db,
      COLLECTIONS.GENERATED_RECIPES,
      userId,
      SUBCOLLECTIONS.GENERATED_RECIPES.RECIPES
    );
    let recipesQuery = query(recipesCollectionRef, orderBy('generatedAt', 'desc'), limit(pageSize));

    // If we have a cursor, start after the last visible document
    if (lastVisible) {
      recipesQuery = query(
        recipesCollectionRef,
        orderBy('generatedAt', 'desc'),
        startAfter(lastVisible),
        limit(pageSize)
      );
    }

    const querySnapshot = await getDocs(recipesQuery);

    if (querySnapshot.empty) {
      return {
        data: [],
        lastVisible: null,
        hasMore: false,
      };
    }

    // Extract recipe data from documents
    const recipes = querySnapshot.docs.map((doc: QueryDocumentSnapshot) => ({
      id: doc.id,
      ...doc.data(),
    }));

    // Get the last visible document for next pagination
    const newLastVisible = querySnapshot.docs[querySnapshot.docs.length - 1];

    // Check if there are more documents by trying to fetch one more
    const nextQuery = query(recipesCollectionRef, orderBy('generatedAt', 'desc'), startAfter(newLastVisible), limit(1));
    const nextSnapshot = await getDocs(nextQuery);
    const hasMore = !nextSnapshot.empty;

    return {
      data: recipes,
      lastVisible: newLastVisible,
      hasMore,
    };
  } catch (error) {
    console.error('Error fetching paginated recipes:', error);
    return {
      data: [],
      lastVisible: null,
      hasMore: false,
    };
  }
};

export const firestoreRepository: FirestoreRepository = {
  getCollection,
  addDocument,
  getDocument,
  updateDocument,
  deleteDocument,
  addOrReplaceDocument,
  getSubcollection,
  addSubcollectionDocument,
  addSubcollectionDocumentWithAutoId,
  deleteSubcollectionDocument,
  getPaginatedRecipes,
};
