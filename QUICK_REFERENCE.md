# Timezone-Aware Coaching Messages - Quick Reference

## 🎯 What Changed?
Coaching messages now send at **3 PM in each user's local timezone** instead of 9 AM UTC for everyone.

## ⚡ Quick Start

### 1. Install Backend Dependencies
```bash
cd functions && pip install -r requirements.txt && cd ..
```

### 2. Deploy Function
```bash
firebase deploy --only functions:send_daily_coaching_messages
```

### 3. Done! ✅
Timezone is automatically saved when users complete onboarding.
Users will now receive messages at 3 PM their time.

---

## 📋 Firestore Schema

### Required Field in `users/{uid}`
```typescript
{
  timezone: "America/New_York"  // IANA timezone string
}
```

### Auto-Generated Field
```typescript
{
  lastCoachingMessageDate: "2025-10-06"  // Prevents duplicates
}
```

---

## 🔧 Common Tasks

### Get User's Timezone
```typescript
import * as Localization from 'expo-localization';
const timezone = Localization.timezone; // e.g., "America/Chicago"
```

### Save User's Timezone
```typescript
import { doc, setDoc } from '@react-native-firebase/firestore';
import { db } from '@/firebase/firebaseConfig';

await setDoc(doc(db, 'users', uid), {
  timezone: 'America/New_York',
  timezoneUpdatedAt: new Date()
}, { merge: true });
```

---

## 🐛 Troubleshooting

### User Not Receiving Messages?

**Check 1:** Does user have timezone?
```typescript
import { doc, getDoc } from '@react-native-firebase/firestore';
import { db } from '@/firebase/firebaseConfig';

const userDoc = await getDoc(doc(db, 'users', uid));
console.log('Timezone:', userDoc.data()?.timezone);
```

**Check 2:** Is it 3 PM in their timezone?
Check Cloud Function logs to see if the function is processing the user.

**Check 3:** Already sent today?
```typescript
console.log('Last sent:', userDoc.data()?.lastCoachingMessageDate);
console.log('Today:', new Date().toISOString().split('T')[0]);
```

**Fix:** Set timezone manually
```typescript
import { doc, setDoc } from '@react-native-firebase/firestore';
import { db } from '@/firebase/firebaseConfig';

await setDoc(doc(db, 'users', uid), { timezone: 'America/New_York' }, { merge: true });
```

---

## 📊 Monitoring

### View Logs
Firebase Console → Functions → `send_daily_coaching_messages` → Logs

### Key Log Messages
- ✅ `Starting hourly coaching message check` - Function running
- ✅ `Generating coaching message for user` - Sending message
- ⚠️ `No timezone set for user` - User needs timezone
- ⚠️ `Already sent coaching message today` - Duplicate prevented
- ✅ `Hourly coaching message check completed` - Summary

---

## 🌍 Common Timezones

**Popular Timezones:**
- `America/New_York` - Eastern Time
- `America/Chicago` - Central Time
- `America/Denver` - Mountain Time
- `America/Los_Angeles` - Pacific Time
- `Europe/London` - GMT/BST
- `Asia/Tokyo` - JST

---

## 🧪 Testing

### Test Timezone Detection
```typescript
import * as Localization from 'expo-localization';
console.log('Device timezone:', Localization.timezone);
```

### Test with Different Timezone
```typescript
import { doc, setDoc } from '@react-native-firebase/firestore';
import { db } from '@/firebase/firebaseConfig';

// Set your timezone to one where it's currently 3 PM
await setDoc(doc(db, 'users', uid), { timezone: 'America/Los_Angeles' }, { merge: true });
// Wait for next hour, check logs
```

### Verify in Firestore
1. Open Firebase Console
2. Go to Firestore Database
3. Navigate to `users/{your-uid}`
4. Check `timezone` field exists

---

## 📚 Documentation

- **Full Details:** `functions/TIMEZONE_COACHING_MESSAGES.md`
- **Setup Guide:** `TIMEZONE_SETUP_INSTRUCTIONS.md`
- **Changes Summary:** `CHANGES_SUMMARY.md`
- **Code Reference:** `utils/timezoneHelper.ts`

---

## 🚀 How It Works

```
Every Hour:
  ├─ Get all users with diet preferences
  ├─ For each user:
  │   ├─ Get timezone from Firestore
  │   ├─ Convert UTC → User's local time
  │   ├─ Is it 3 PM? → No: Skip
  │   ├─ Already sent today? → Yes: Skip
  │   └─ Send message + Update lastCoachingMessageDate
  └─ Log summary
```

---

## 💡 Pro Tips

1. **Auto-detect timezone** on every app launch (already done in AuthContext)
2. **Validate timezone** before saving: `isValidTimezone(tz)`
3. **Monitor logs** regularly to catch issues early
4. **Test thoroughly** before deploying to production
5. **Document timezone** in user settings if you add manual selection

---

## 🔄 Migration for Existing Users

### Option 1: Automatic (Recommended)
New users automatically get timezone when completing onboarding.

### Option 2: Manual Migration
For existing users, manually set a default timezone in Firestore Console or use Firebase Admin SDK to batch update users without a timezone field.

---

## ⚙️ Configuration

### Change Message Time (from 3 PM to another hour)
Edit `functions/main.py` line 1007:
```python
# Change 15 to desired hour (0-23)
if user_hour != 15:  # 15 = 3 PM
```

### Change Schedule Frequency
Edit `functions/main.py` line 924:
```python
# Current: Every hour
@scheduler_fn.on_schedule(schedule="0 * * * *", ...)

# Every 30 minutes:
@scheduler_fn.on_schedule(schedule="*/30 * * * *", ...)

# Every 2 hours:
@scheduler_fn.on_schedule(schedule="0 */2 * * *", ...)
```

---

## 📞 Support

**Issues?**
1. Check logs in Firebase Console
2. Review troubleshooting section above
3. Check full documentation in `TIMEZONE_SETUP_INSTRUCTIONS.md`

**Common Errors:**
- `pytz not found` → Run `pip install -r requirements.txt`
- `Localization.timezone undefined` → Run `npx expo install expo-localization`
- `Invalid timezone` → Use valid IANA timezone string
- `No timezone set` → User needs to complete onboarding or manually set timezone

---

## ✅ Checklist

**Backend:**
- [ ] Installed pytz: `pip install -r requirements.txt`
- [ ] Deployed function: `firebase deploy --only functions`
- [ ] Verified function runs hourly (check logs)

**Frontend:**
- [ ] Verified expo-localization is installed (comes with Expo)
- [ ] Tested timezone detection works
- [ ] Verified timezone saves to Firestore during onboarding

**Testing:**
- [ ] Set test user timezone
- [ ] Waited for 3 PM in that timezone
- [ ] Received coaching message
- [ ] Verified no duplicate message same day
- [ ] Checked logs for correct execution

---

## 🎉 You're Done!

Users will now receive personalized coaching messages at 3 PM in their local timezone, no matter where they are in the world! 🌍

