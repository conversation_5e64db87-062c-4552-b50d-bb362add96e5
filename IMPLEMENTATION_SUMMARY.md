# Timezone-Aware Coaching Messages - Implementation Summary

## ✅ What Was Done

### Backend Changes (Cloud Functions)

#### 1. Modified `functions/main.py`
- **Schedule Change**: `0 9 * * *` (daily at 9 AM UTC) → `0 * * * *` (every hour)
- **Added Timezone Logic**:
  - Imports `pytz` for timezone handling
  - Gets current UTC time at start of each run
  - For each user with diet preferences:
    - Fetches timezone from `users/{uid}` document
    - Converts UTC to user's local time
    - Checks if it's 3 PM (15:00) in their timezone
    - Checks if message already sent today
    - Only sends if all conditions met
  - Updates `lastCoachingMessageDate` after sending

#### 2. Updated `functions/requirements.txt`
- Added `pytz` dependency for Python timezone handling

### Frontend Changes (Mobile App)

#### 1. Modified `app/onboarding.tsx`
- **Added Import**: `import * as Localization from 'expo-localization';`
- **Modified `handleProceedToCamera` function**:
  - Gets user's timezone: `const timezone = Localization.timezone;`
  - Saves timezone in parallel with diet preferences using `Promise.all()`
  - Saves to `users/{uid}` collection with fields: `timezone`, `timezoneUpdatedAt`

**Key Code:**
```typescript
// Get user's timezone
const timezone = Localization.timezone; // e.g. "America/Chicago"

// Save diet preferences and timezone in parallel
await Promise.all([
  firestoreRepository.addOrReplaceDocument(COLLECTIONS.DIET_PREFERENCES, user.uid, transformedPreferences),
  firestoreRepository.addOrReplaceDocument(COLLECTIONS.USERS, user.uid, { 
    timezone, 
    timezoneUpdatedAt: new Date() 
  }),
]);
```

### Documentation Created

1. **`functions/TIMEZONE_COACHING_MESSAGES.md`** - Detailed technical documentation
2. **`TIMEZONE_SETUP_INSTRUCTIONS.md`** - Step-by-step setup guide
3. **`QUICK_REFERENCE.md`** - Quick reference for developers
4. **`CHANGES_SUMMARY.md`** - Comprehensive changes summary
5. **`IMPLEMENTATION_SUMMARY.md`** - This file

## 🎯 How It Works

### Flow Diagram
```
Every Hour (on the hour):
  ├─ Cloud Function triggers
  ├─ Get current UTC time
  ├─ For each user with diet preferences:
  │   ├─ Get user document from Firestore
  │   ├─ Check if timezone field exists
  │   │   ├─ No → Skip user (log: "No timezone set")
  │   │   └─ Yes → Continue
  │   ├─ Convert UTC to user's local time
  │   ├─ Check if local hour == 15 (3 PM)
  │   │   ├─ No → Skip user (not 3 PM)
  │   │   └─ Yes → Continue
  │   ├─ Check lastCoachingMessageDate
  │   │   ├─ Already sent today → Skip (log: "Already sent")
  │   │   └─ Not sent today → Continue
  │   ├─ Get/create coach DM conversation
  │   ├─ Fetch recent coaching messages (context)
  │   ├─ Generate personalized tip with OpenAI
  │   ├─ Send message to conversation
  │   └─ Update lastCoachingMessageDate
  └─ Log summary (messages sent, users processed, failed, skipped)
```

### Example Scenario
**Current UTC Time**: 2025-10-06 19:00:00 UTC

| User | Timezone | Local Time | Hour | lastCoachingMessageDate | Action |
|------|----------|------------|------|------------------------|--------|
| Alice | America/New_York (UTC-4) | 15:00 EDT | 15 | null | ✅ **Send message** |
| Bob | America/New_York (UTC-4) | 15:00 EDT | 15 | 2025-10-06 | ❌ Skip (already sent) |
| Carol | Europe/London (UTC+1) | 20:00 BST | 20 | null | ❌ Skip (not 3 PM) |
| Dave | Asia/Tokyo (UTC+9) | 04:00 JST | 4 | null | ❌ Skip (not 3 PM) |
| Eve | America/Los_Angeles (UTC-7) | 12:00 PDT | 12 | null | ❌ Skip (not 3 PM) |

**Next Hour (20:00 UTC)**:
- Alice: 16:00 EDT → Skip (not 3 PM, already sent today)
- Eve: 13:00 PDT → Skip (not 3 PM)

**Three Hours Later (22:00 UTC)**:
- Eve: 15:00 PDT → ✅ **Send message** (3 PM, not sent today)

## 📋 Firestore Schema

### Users Collection
```typescript
users/{uid} {
  // Existing fields
  lastActiveAt: Date,
  
  // New fields
  timezone: string,  // IANA timezone (e.g., "America/New_York")
  timezoneUpdatedAt: Date,  // When timezone was last updated
  lastCoachingMessageDate?: string,  // Format: "YYYY-MM-DD", auto-generated by function
}
```

## 🚀 Deployment Steps

### 1. Install Backend Dependencies
```bash
cd functions
pip install -r requirements.txt
cd ..
```

### 2. Deploy Cloud Function
```bash
firebase deploy --only functions:send_daily_coaching_messages
```

### 3. Test
- Complete onboarding in the app (timezone automatically saved)
- Check Firestore: `users/{uid}` should have `timezone` field
- Wait for 3 PM in your timezone
- Check Cloud Function logs
- Verify coaching message received

## ✨ Key Features

### 1. Automatic Timezone Detection
- Uses `expo-localization` (already included with Expo)
- Detects device timezone automatically
- Saved during onboarding (no extra user action needed)

### 2. Parallel Writes
- Timezone and diet preferences saved simultaneously
- Uses `Promise.all()` for efficiency
- No performance impact on onboarding

### 3. Duplicate Prevention
- Tracks `lastCoachingMessageDate` per user
- Prevents multiple messages in same day
- Even if function runs multiple times during 3 PM hour

### 4. Robust Error Handling
- Skips users without timezone (logs reason)
- Validates timezone strings
- Continues processing other users if one fails
- Comprehensive logging for debugging

## 📊 Monitoring

### Cloud Function Logs
View in Firebase Console → Functions → `send_daily_coaching_messages`

**Key Log Messages:**
```
✅ Starting hourly coaching message check at 2025-10-06 19:00:00+00:00
✅ Generating coaching message for user abc123 (local time: 2025-10-06 15:00 EDT)
✅ Successfully sent coaching message to user abc123 in conversation xyz789
⚠️ No timezone set for user def456, skipping
⚠️ Already sent coaching message to user ghi789 today (2025-10-06), skipping
✅ Hourly coaching message check completed. Messages sent: 1, Users processed: 1, Failed: 0, Skipped: 2
```

## 🧪 Testing

### Test Timezone Detection
```typescript
import * as Localization from 'expo-localization';
console.log('Device timezone:', Localization.timezone);
```

### Test Timezone Saved
1. Complete onboarding
2. Open Firestore Console
3. Navigate to `users/{your-uid}`
4. Verify `timezone` field exists

### Test Message Delivery
1. Manually set your timezone in Firestore to one where it's currently 3 PM
2. Wait for next hour (function runs on the hour)
3. Check Cloud Function logs
4. Verify message received in app

### Manual Timezone Update
```typescript
import { doc, setDoc } from '@react-native-firebase/firestore';
import { db } from '@/firebase/firebaseConfig';

await setDoc(doc(db, 'users', uid), { 
  timezone: 'America/Los_Angeles' 
}, { merge: true });
```

## 🔧 Troubleshooting

### User Not Receiving Messages

**Checklist:**
1. ✅ User has `timezone` field in Firestore?
2. ✅ Timezone is valid IANA string?
3. ✅ It's actually 3 PM in their timezone?
4. ✅ `lastCoachingMessageDate` is not today?
5. ✅ User has diet preferences?
6. ✅ Cloud Function is running hourly?

**Check User Document:**
```typescript
import { doc, getDoc } from '@react-native-firebase/firestore';
import { db } from '@/firebase/firebaseConfig';

const userDoc = await getDoc(doc(db, 'users', uid));
console.log('User data:', userDoc.data());
```

**Fix Missing Timezone:**
```typescript
await setDoc(doc(db, 'users', uid), { 
  timezone: 'America/New_York' 
}, { merge: true });
```

### Common Errors

| Error | Cause | Solution |
|-------|-------|----------|
| `pytz not found` | Missing dependency | `pip install -r requirements.txt` |
| `No timezone set for user` | User hasn't completed onboarding | Complete onboarding or manually set timezone |
| `Invalid timezone` | Invalid IANA string | Use valid timezone (e.g., "America/New_York") |
| `Already sent today` | Message already sent | This is expected behavior, not an error |

## 📚 Documentation Reference

- **Quick Start**: `QUICK_REFERENCE.md`
- **Detailed Setup**: `TIMEZONE_SETUP_INSTRUCTIONS.md`
- **Technical Details**: `functions/TIMEZONE_COACHING_MESSAGES.md`
- **All Changes**: `CHANGES_SUMMARY.md`

## 🎉 Benefits

1. **User-Friendly**: Messages arrive at 3 PM local time, regardless of location
2. **Automatic**: No user configuration needed
3. **Efficient**: Parallel writes, hourly checks
4. **Reliable**: Duplicate prevention, error handling
5. **Scalable**: Works for users in any timezone worldwide
6. **Simple**: Uses existing Expo package, minimal code changes

## 🔮 Future Enhancements

1. **Customizable Time**: Let users choose their preferred message time
2. **Frequency Control**: Daily, weekly, or custom frequency options
3. **Timezone Settings UI**: Manual timezone selection screen
4. **Smart Timing**: ML-based optimal message timing
5. **Multi-timezone Support**: Handle users traveling across timezones

## ✅ Checklist

- [x] Backend function updated
- [x] Timezone detection added to onboarding
- [x] Parallel writes implemented
- [x] Duplicate prevention added
- [x] Documentation created
- [ ] Backend dependencies installed
- [ ] Cloud function deployed
- [ ] Tested with real users
- [ ] Existing users migrated (optional)

## 🎯 Success Criteria

✅ **Implementation Complete When:**
1. Cloud function runs every hour
2. New users get timezone saved during onboarding
3. Messages sent at 3 PM local time
4. No duplicate messages same day
5. Comprehensive logs for monitoring
6. All documentation complete

**Status**: ✅ **Implementation Complete** - Ready for deployment!

