{"name": "chefpal", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "USE_MOCK_RECIPES=false expo start -c", "start:mock": "USE_MOCK_RECIPES=true expo start -c", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-firebase/analytics": "^23.4.0", "@react-native-firebase/app": "^23.4.0", "@react-native-firebase/auth": "^23.4.0", "@react-native-firebase/crashlytics": "^23.4.0", "@react-native-firebase/firestore": "^23.4.0", "@react-native-firebase/messaging": "^23.4.0", "@react-native-firebase/perf": "^23.4.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "expo": "^53.0.5", "expo-blur": "~14.1.4", "expo-build-properties": "~0.14.8", "expo-camera": "~16.1.6", "expo-constants": "~17.1.5", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.0", "expo-haptics": "~14.1.4", "expo-linking": "~7.1.4", "expo-localization": "^17.0.7", "expo-router": "~5.0.4", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "fuse.js": "^7.1.0", "openai": "^4.97.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-adapty": "^3.6.1", "react-native-bouncy-checkbox": "^4.1.2", "react-native-gesture-handler": "~2.24.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-paper": "^5.14.0", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-share": "^12.1.0", "react-native-svg": "15.11.2", "react-native-svg-transformer": "^1.5.1", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.27.1", "@types/estree": "^1.0.8", "@types/jest": "^29.5.14", "@types/react": "~19.1.2", "@types/react-native": "^0.73.0", "@types/react-test-renderer": "^19.0.0", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "expo-module-scripts": "^4.1.6", "jest": "^29.7.0", "react-native-dotenv": "^3.4.11", "react-test-renderer": "19.0.0", "typescript": "^5.8.3"}, "private": true, "expo": {"plugins": [["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera to take photos of your ingredients."}]]}}