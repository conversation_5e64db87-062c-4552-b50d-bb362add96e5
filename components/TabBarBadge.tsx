import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface TabBarBadgeProps {
  count: number;
  color?: string;
}

/**
 * Badge component to display unread count on tab bar icons
 * Shows a red circle with white text for counts > 0
 * Displays "99+" for counts >= 100
 */
export function TabBarBadge({ count, color = '#FF3B30' }: TabBarBadgeProps) {
  if (count <= 0) {
    return null;
  }

  const displayCount = count >= 100 ? '99+' : count.toString();

  return (
    <View style={[styles.badge, { backgroundColor: color }]}>
      <Text style={styles.badgeText}>{displayCount}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  badge: {
    position: 'absolute',
    top: -4,
    right: -10,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: '#FF3B30',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

