import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  TouchableWithoutFeedback,
  useColorScheme,
} from 'react-native';
import createChatStyles from '@/styles/ChatStyles';
import { getThemeColors } from '@/styles/Theme';
import { DisplayMessage } from '@/constants/Types';
import TypingIndicator from '@/components/TypingIndicator';

interface ChatUIProps {
  messages: DisplayMessage[];
  inputValue: string;
  onInputChange: (text: string) => void;
  onSendMessage: () => void;
  onTakePicture?: () => void;
  isLoading?: boolean;
  isSending?: boolean;
  placeholder?: string;
  containerStyle?: object;
}

const ChatUI: React.FC<ChatUIProps> = ({
  messages,
  inputValue,
  onInputChange,
  onSendMessage,
  onTakePicture,
  isLoading = false,
  isSending = false,
  placeholder = 'Type a message...',
  containerStyle = {},
}) => {
  const colorScheme = useColorScheme() || 'light';
  const styles = createChatStyles(colorScheme as 'light' | 'dark');
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  const flatListRef = useRef<FlatList>(null);

  // Scroll to the bottom whenever messages update
  useEffect(() => {
    if (flatListRef.current && messages.length > 0) {
      // Use a slight delay if the last message is not from the user
      const delay = messages[messages.length - 1]?.type === 'user' ? 0 : 20;
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, delay);
    }
  }, [messages]);

  const renderMessage = ({ item, index }: { item: DisplayMessage; index: number }) => {
    const isUser = item.type === 'user';
    const isSystem = item.type === 'system';

    return (
      <View key={item.id || index}>
        <View style={styles.chatListItem}>
          <Text
            style={[
              isUser ? styles.conversationUser : styles.conversationBot,
              { alignSelf: isUser ? 'flex-end' : 'flex-start' },
              isSystem && { fontStyle: 'italic', opacity: 0.8 },
            ]}
          >
            {item.text}
          </Text>
        </View>

        {/* Show typing indicator after the last message */}
        {index === messages.length - 1 && (isLoading || isSending) && (
          <View style={styles.chatListItem}>
            <View style={[styles.conversationBot, { alignSelf: 'flex-start' }]}>
              <TypingIndicator />
            </View>
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.safeArea}>
        <FlatList
          ref={flatListRef}
          data={messages}
          keyExtractor={(item, index) => item.id || index.toString()}
          renderItem={renderMessage}
          onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
          onLayout={() => flatListRef.current?.scrollToEnd({ animated: true })}
          style={styles.chatList}
          contentContainerStyle={styles.chatListContent}
          showsVerticalScrollIndicator={false}
        />
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidViewWithTab}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.chatInput}
              multiline={true}
              onChangeText={onInputChange}
              value={inputValue}
              placeholder={placeholder}
              placeholderTextColor={colors.inputPlaceholder}
            />
            <TouchableOpacity style={styles.sendButton} onPress={onSendMessage}>
              <Text style={styles.sendButtonText}>→</Text>
            </TouchableOpacity>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </View>
  );
};

export default ChatUI;