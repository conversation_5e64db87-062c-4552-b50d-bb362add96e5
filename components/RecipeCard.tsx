import React, { useMemo } from 'react';
import { View, useColorScheme, TouchableOpacity, LayoutAnimation, Platform, UIManager } from 'react-native';
import { Card, Text, IconButton, Button, Divider, ActivityIndicator } from 'react-native-paper';
import createRecipeStyles from '@/styles/RecipeStyles';
import { getThemeColors } from '@/styles/Theme';
import { Recipe, InstructionType } from '@/components/types';
import ShareButton, { ShareButtonType } from '@/components/ShareButton';
import SmoothImage from '@/components/SmoothImage';
import ShoppingCart from '@/assets/images/icons/shopping-cart.svg';
import { useGroceryList } from '@/contexts/GroceryListContext';
import { useInventory } from '@/contexts/InventoryContext';
import { computeIngredientAvailability } from '@/utils/ingredientUtils';
import logger from '@/services/logger';

// Enable LayoutAnimation on Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

interface RecipeCardProps {
  recipe: Recipe;
  isExpanded: boolean;
  isFavorite: boolean;
  servings: number;
  instructionType: InstructionType;
  instructionTypes: InstructionType[];
  isLoadingDetails: boolean;
  isGeneratingDetails: boolean;
  onToggleFavorite: (e: any) => void;
  onToggleExpanded: () => void;
  onSelectInstructionType: (type: InstructionType) => void;
  onChangeServings: (servings: number) => void;
}

const RecipeCard: React.FC<RecipeCardProps> = ({
  recipe,
  isExpanded,
  isFavorite,
  servings,
  instructionType,
  instructionTypes,
  isLoadingDetails,
  isGeneratingDetails,
  onToggleFavorite,
  onToggleExpanded,
  onSelectInstructionType,
  onChangeServings,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const RecipeStyles = createRecipeStyles(colorScheme as 'light' | 'dark');
  const colors = getThemeColors(colorScheme as 'light' | 'dark');

  const { addItem, addItems } = useGroceryList();
  const { isIngredientAvailable } = useInventory();

  // Configure layout animation for smooth expansion
  const configureLayoutAnimation = () => {
    LayoutAnimation.configureNext({
      duration: 200,
      create: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
      update: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
    });
  };

  const handlePress = () => {
    configureLayoutAnimation();
    onToggleExpanded();
  };

  // Compute ingredients with availability (memoized to prevent recalculation)
  const ingredientsWithAvailability = useMemo(() => {
    return computeIngredientAvailability(recipe.ingredients, isIngredientAvailable);
  }, [recipe.ingredients, isIngredientAvailable]);

  const addIngredientToGroceryList = async (ingredientName: string) => {
    try {
      await addItem(ingredientName);
    } catch (error) {
      logger.dataError('grocery_ingredient_add', error instanceof Error ? error.message : 'Unknown error', {
        additionalData: { ingredientName, recipeId: recipe.id },
      });
    }
  };

  const addAllIngredientsToGroceryList = async () => {
    try {
      const unavailableIngredients = ingredientsWithAvailability
        .filter((ingredient) => !ingredient.available)
        .map((ingredient) => ingredient.name);

      if (unavailableIngredients.length > 0) {
        await addItems(unavailableIngredients);
      }
    } catch (error) {
      logger.dataError('grocery_ingredients_bulk_add', error instanceof Error ? error.message : 'Unknown error', {
        additionalData: { recipeId: recipe.id, ingredientCount: ingredientsWithAvailability.length },
      });
    }
  };

  return (
    <Card style={RecipeStyles.recipeCard} onPress={handlePress}>
      <View style={RecipeStyles.recipeCardContent}>
        {/* Header - always visible */}
        <View style={RecipeStyles.recipeHeader}>
          <View style={RecipeStyles.recipeHeaderContent}>
            <Text style={RecipeStyles.recipeTitle} numberOfLines={isExpanded ? undefined : 2}>
              {recipe.title}
            </Text>
            <Text style={RecipeStyles.recipeInfo}>
              {recipe.timeInMinutes} min · {recipe.calories} calories per serving
            </Text>
          </View>
          <View style={RecipeStyles.recipeActionButtons}>
            <IconButton
              icon={isFavorite ? 'heart' : 'heart-outline'}
              iconColor={colors.accent}
              size={24}
              onPress={(e) => {
                e.stopPropagation();
                onToggleFavorite(e);
              }}
              style={RecipeStyles.favoriteButton}
            />
            <ShareButton
              type={isExpanded ? ShareButtonType.RECIPE : ShareButtonType.BASIC_RECIPE}
              recipe={recipe}
              instructionType={isExpanded ? instructionType : undefined}
              servings={isExpanded ? servings : undefined}
              size={24}
              color={colors.accent}
            />
          </View>
        </View>

        <View style={RecipeStyles.imageContainer}>
          <SmoothImage source={{ uri: recipe.imageUrl }} style={RecipeStyles.recipeImage} resizeMode='cover' />
        </View>

        {/* Expanded content - only visible when expanded */}
        {isExpanded && (
          <>
            {/* Loading Indicator */}
            {(isLoadingDetails || isGeneratingDetails) && (
              <View style={{ alignItems: 'center', padding: 20 }}>
                <ActivityIndicator size='large' color={colors.accent} />
                <Text style={{ marginTop: 10, color: colors.text, textAlign: 'center' }}>
                  Loading recipe details...
                </Text>
              </View>
            )}

            {/* Ingredients and Instructions - only show when not loading */}
            {!isLoadingDetails && !isGeneratingDetails && (
              <>
                {/* Ingredients Section */}
                <View style={RecipeStyles.sectionContainer}>
                  <Text style={RecipeStyles.sectionTitle}>Ingredients</Text>

                  <View style={RecipeStyles.servingsContainer}>
                    <Text style={RecipeStyles.servingsLabel}>Servings</Text>
                    <TouchableOpacity
                      style={RecipeStyles.servingButton}
                      onPress={() => onChangeServings(Math.max(1, servings - 1))}
                    >
                      <Text style={RecipeStyles.servingButtonText}>−</Text>
                    </TouchableOpacity>
                    <Text style={RecipeStyles.servingsCount}>{servings}</Text>
                    <TouchableOpacity style={RecipeStyles.servingButton} onPress={() => onChangeServings(servings + 1)}>
                      <Text style={RecipeStyles.servingButtonText}>+</Text>
                    </TouchableOpacity>
                  </View>

                  {/* Ingredients List */}
                  <View>
                    {ingredientsWithAvailability.map((ingredient, index) => (
                      <View
                        key={index}
                        style={
                          !ingredient.available ? RecipeStyles.unavailableIngredientRow : RecipeStyles.ingredientRow
                        }
                      >
                        <Text
                          style={[
                            RecipeStyles.ingredientText,
                            !ingredient.available && RecipeStyles.unavailableIngredientText,
                          ]}
                        >
                          {ingredient.amount} {ingredient.unit} {ingredient.name}
                        </Text>
                        {!ingredient.available && (
                          <IconButton
                            icon={() => <ShoppingCart fill={colors.accent} width={18} height={18} />}
                            onPress={() => addIngredientToGroceryList(ingredient.name)}
                          />
                        )}
                      </View>
                    ))}
                  </View>

                  <Button
                    mode='outlined'
                    style={RecipeStyles.addAllButton}
                    icon={() => <ShoppingCart fill={colors.accent} width={18} height={18} />}
                    onPress={addAllIngredientsToGroceryList}
                    textColor={colors.accent}
                  >
                    Add All to Grocery List
                  </Button>
                </View>

                <Divider />

                {/* Instructions Section */}
                <View style={RecipeStyles.sectionContainer}>
                  <Text style={RecipeStyles.sectionTitle}>Instructions</Text>

                  {/* Instruction Type Selector */}
                  <View style={RecipeStyles.instructionTypesContainer}>
                    {instructionTypes.map((type) => (
                      <TouchableOpacity
                        key={type}
                        style={[
                          RecipeStyles.instructionTypeChip,
                          instructionType === type && { backgroundColor: colors.accent },
                        ]}
                        onPress={() => onSelectInstructionType(type)}
                      >
                        <Text
                          style={[
                            RecipeStyles.instructionTypeText,
                            instructionType === type && { color: colors.background },
                          ]}
                        >
                          {type.replace('_', ' ')}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>

                  {/* Instructions Text */}
                  <Text style={RecipeStyles.instructionsText}>
                    {recipe.instructions[instructionType] || 'Instructions not available'}
                  </Text>
                </View>
              </>
            )}
          </>
        )}
      </View>
    </Card>
  );
};

export default RecipeCard;
