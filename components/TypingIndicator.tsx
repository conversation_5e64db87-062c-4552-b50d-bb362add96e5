import React, { useEffect, useRef } from 'react';
import { View, Animated, StyleSheet, useColorScheme } from 'react-native';
import { getThemeColors } from '@/styles/Theme';

interface TypingIndicatorProps {
  dotColor?: string;
  dotSize?: number;
  space?: number;
}

/**
 * Animated typing indicator component that shows three dots bouncing
 * Used to indicate when the chat is loading or the LLM is processing
 */
const TypingIndicator: React.FC<TypingIndicatorProps> = ({ 
  dotColor, 
  dotSize = 6, 
  space = 4 
}) => {
  const colorScheme = useColorScheme() || 'light';
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  
  // Use theme color if no custom color is provided
  const finalDotColor = dotColor || colors.statusText;
  
  const anims = [
    useRef(new Animated.Value(0)).current,
    useRef(new Animated.Value(0)).current,
    useRef(new Animated.Value(0)).current,
  ];

  useEffect(() => {
    const animations = anims.map((anim, i) =>
      Animated.loop(
        Animated.sequence([
          Animated.delay(i * 200),
          Animated.timing(anim, { 
            toValue: 1, 
            duration: 300, 
            useNativeDriver: true 
          }),
          Animated.timing(anim, { 
            toValue: 0.3, 
            duration: 300, 
            useNativeDriver: true 
          }),
        ])
      )
    );
    Animated.parallel(animations).start();
  }, [anims]);

  return (
    <View style={styles.container}>
      {anims.map((anim, i) => (
        <Animated.View
          key={i}
          style={{
            width: dotSize,
            height: dotSize,
            backgroundColor: finalDotColor,
            borderRadius: dotSize / 2,
            marginHorizontal: space / 2,
            opacity: anim,
            transform: [{ scale: anim }],
          }}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { 
    flexDirection: 'row', 
    alignItems: 'center', 
    height: 20,
    paddingHorizontal: 4,
  },
});

export default TypingIndicator;

